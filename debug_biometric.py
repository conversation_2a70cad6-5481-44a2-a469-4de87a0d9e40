#!/usr/bin/env python3
"""
Debug script to check biometric verification issues
"""

import sqlite3
import datetime
import requests
import json

def check_database_structure():
    """Check if database has all required tables and columns"""
    print("🔍 Checking database structure...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check attendance table
        cursor.execute("PRAGMA table_info(attendance)")
        attendance_columns = [col[1] for col in cursor.fetchall()]
        print(f"📊 Attendance table columns: {attendance_columns}")
        
        required_columns = ['id', 'staff_id', 'school_id', 'date', 'time_in', 'time_out', 'overtime_in', 'overtime_out', 'status']
        missing_columns = [col for col in required_columns if col not in attendance_columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
        
        # Check staff table
        cursor.execute("PRAGMA table_info(staff)")
        staff_columns = [col[1] for col in cursor.fetchall()]
        print(f"👥 Staff table columns: {staff_columns}")
        
        # Check biometric_verifications table
        cursor.execute("PRAGMA table_info(biometric_verifications)")
        verification_columns = [col[1] for col in cursor.fetchall()]
        print(f"🔐 Biometric verifications table columns: {verification_columns}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False
    finally:
        db.close()

def check_existing_staff():
    """Check if there are any staff members in the database"""
    print("\n👥 Checking existing staff...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        cursor.execute("SELECT id, staff_id, full_name, school_id FROM staff")
        staff_members = cursor.fetchall()
        
        if staff_members:
            print(f"✅ Found {len(staff_members)} staff members:")
            for staff in staff_members:
                print(f"   ID: {staff[0]}, Staff ID: {staff[1]}, Name: {staff[2]}, School: {staff[3]}")
            return staff_members
        else:
            print("❌ No staff members found in database")
            return []
            
    except Exception as e:
        print(f"❌ Error checking staff: {e}")
        return []
    finally:
        db.close()

def check_attendance_records():
    """Check existing attendance records"""
    print("\n📋 Checking attendance records...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        today = datetime.date.today()
        cursor.execute("""
            SELECT a.id, a.staff_id, s.full_name, a.date, a.time_in, a.time_out, 
                   a.overtime_in, a.overtime_out, a.status
            FROM attendance a
            JOIN staff s ON a.staff_id = s.id
            WHERE a.date = ?
        """, (today,))
        
        records = cursor.fetchall()
        
        if records:
            print(f"✅ Found {len(records)} attendance records for today:")
            for record in records:
                print(f"   Staff: {record[2]}, In: {record[4]}, Out: {record[5]}, OT-In: {record[6]}, OT-Out: {record[7]}, Status: {record[8]}")
        else:
            print("❌ No attendance records found for today")
            
        return records
        
    except Exception as e:
        print(f"❌ Error checking attendance: {e}")
        return []
    finally:
        db.close()

def check_biometric_verifications():
    """Check biometric verification logs"""
    print("\n🔐 Checking biometric verifications...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        today = datetime.date.today()
        cursor.execute("""
            SELECT bv.id, bv.staff_id, s.full_name, bv.verification_type, 
                   bv.verification_time, bv.verification_status, bv.biometric_method
            FROM biometric_verifications bv
            JOIN staff s ON bv.staff_id = s.id
            WHERE DATE(bv.verification_time) = ?
            ORDER BY bv.verification_time DESC
        """, (today,))
        
        verifications = cursor.fetchall()
        
        if verifications:
            print(f"✅ Found {len(verifications)} biometric verifications for today:")
            for verification in verifications:
                print(f"   Staff: {verification[2]}, Type: {verification[3]}, Time: {verification[4]}, Status: {verification[5]}")
        else:
            print("❌ No biometric verifications found for today")
            
        return verifications
        
    except Exception as e:
        print(f"❌ Error checking verifications: {e}")
        return []
    finally:
        db.close()

def test_biometric_endpoint():
    """Test the biometric attendance endpoint"""
    print("\n🧪 Testing biometric attendance endpoint...")
    
    # First, we need to login to get a session
    session = requests.Session()
    
    try:
        # Get login page to get CSRF token
        login_page = session.get('http://127.0.0.1:5000/')
        if login_page.status_code != 200:
            print(f"❌ Failed to get login page: {login_page.status_code}")
            return False
        
        print("✅ Got login page")
        
        # Try to access biometric endpoint without login (should fail)
        response = session.post('http://127.0.0.1:5000/biometric_attendance', data={
            'verification_type': 'check-in',
            'biometric_method': 'fingerprint'
        })
        
        print(f"📡 Biometric endpoint response: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📄 Response data: {data}")
            except:
                print(f"📄 Response text: {response.text[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def create_test_staff_and_login():
    """Create a test staff member and try to login"""
    print("\n👤 Creating test staff member...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check if test staff exists
        cursor.execute("SELECT id FROM staff WHERE staff_id = 'TEST001'")
        existing = cursor.fetchone()
        
        if not existing:
            from werkzeug.security import generate_password_hash
            password_hash = generate_password_hash('test123')
            
            cursor.execute("""
                INSERT INTO staff (staff_id, full_name, department, position, school_id, password_hash)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('TEST001', 'Test Staff Member', 'IT Department', 'Developer', 1, password_hash))
            
            staff_id = cursor.lastrowid
            db.commit()
            print(f"✅ Created test staff with ID: {staff_id}")
        else:
            staff_id = existing[0]
            print(f"✅ Test staff already exists with ID: {staff_id}")
        
        return staff_id
        
    except Exception as e:
        print(f"❌ Error creating test staff: {e}")
        return None
    finally:
        db.close()

def run_comprehensive_debug():
    """Run all debug checks"""
    print("🚀 Biometric Verification Debug Tool")
    print("=" * 50)
    
    # Check database structure
    if not check_database_structure():
        print("❌ Database structure issues found. Please run migration script.")
        return
    
    # Check staff members
    staff_members = check_existing_staff()
    
    # Check attendance records
    attendance_records = check_attendance_records()
    
    # Check biometric verifications
    biometric_verifications = check_biometric_verifications()
    
    # Test endpoint
    test_biometric_endpoint()
    
    # Create test staff if needed
    if not staff_members:
        print("\n⚠️  No staff members found. Creating test staff...")
        create_test_staff_and_login()
    
    print("\n" + "=" * 50)
    print("🔍 Debug Summary:")
    print(f"   📊 Database structure: ✅ OK")
    print(f"   👥 Staff members: {len(staff_members)} found")
    print(f"   📋 Today's attendance: {len(attendance_records)} records")
    print(f"   🔐 Today's verifications: {len(biometric_verifications)} records")
    
    if not staff_members:
        print("\n💡 Recommendations:")
        print("   1. Create staff members through admin dashboard")
        print("   2. Make sure staff have login credentials")
        print("   3. Test biometric verification after staff creation")
    
    if not biometric_verifications and staff_members:
        print("\n💡 Possible Issues:")
        print("   1. Staff not logged in to staff dashboard")
        print("   2. Biometric verification failing")
        print("   3. JavaScript errors in browser")
        print("   4. CSRF token issues")

if __name__ == "__main__":
    run_comprehensive_debug()
