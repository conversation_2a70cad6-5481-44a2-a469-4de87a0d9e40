import sqlite3
from werkzeug.security import check_password_hash, generate_password_hash

db = sqlite3.connect('vishnorex.db')
cursor = db.cursor()

# Check admin password
cursor.execute('SELECT id, password FROM admins WHERE username="admin" AND school_id=4')
result = cursor.fetchone()

if result:
    admin_id, password_hash = result
    print(f"Admin ID: {admin_id}")
    print(f"Password hash exists: {password_hash is not None}")
    
    # Test common passwords
    test_passwords = ['admin123', 'admin', 'password', 'test123']
    
    for pwd in test_passwords:
        if check_password_hash(password_hash, pwd):
            print(f"✅ Password is: {pwd}")
            break
    else:
        print("❌ None of the test passwords work")
        print("Setting password to 'admin123'...")
        
        # Update password
        new_hash = generate_password_hash('admin123')
        cursor.execute('UPDATE admins SET password = ? WHERE id = ?', (new_hash, admin_id))
        db.commit()
        print("✅ Password updated to 'admin123'")
else:
    print("❌ Admin not found")

db.close()
