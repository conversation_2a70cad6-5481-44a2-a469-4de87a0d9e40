#!/usr/bin/env python3
"""
ZK Biometric Device Integration Module
Handles connection to ZK biometric devices and attendance data synchronization
"""

from zk import ZK, const
import pymysql
import sqlite3
import datetime
import logging
from typing import List, Dict, Optional
from database import get_db
from flask import current_app

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZKBiometricDevice:
    """ZK Biometric Device Handler"""
    
    def __init__(self, device_ip: str = '*************', port: int = 4370, timeout: int = 5):
        self.device_ip = device_ip
        self.port = port
        self.timeout = timeout
        self.zk = ZK(device_ip, port=port, timeout=timeout)
        self.connection = None
    
    def connect(self) -> bool:
        """Connect to ZK device"""
        try:
            self.connection = self.zk.connect()
            logger.info(f"Successfully connected to ZK device at {self.device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to ZK device: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from ZK device"""
        if self.connection:
            try:
                self.connection.disconnect()
                logger.info("Disconnected from ZK device")
            except Exception as e:
                logger.error(f"Error disconnecting from ZK device: {str(e)}")
    
    def get_attendance_records(self) -> List[Dict]:
        """Get attendance records from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return []
        
        try:
            # Disable device to prevent interference
            self.connection.disable_device()
            
            # Get attendance records
            attendance = self.connection.get_attendance()
            records = []
            
            for record in attendance:
                records.append({
                    'user_id': record.user_id,
                    'timestamp': record.timestamp,
                    'status': record.status,
                    'punch': record.punch,  # 0=Check In, 1=Check Out, 2=Break Out, 3=Break In
                    'verify': getattr(record, 'verify', 0)  # Verification method (safe access)
                })
            
            # Re-enable device
            self.connection.enable_device()
            
            logger.info(f"Retrieved {len(records)} attendance records from ZK device")
            return records
            
        except Exception as e:
            logger.error(f"Error getting attendance records: {str(e)}")
            # Make sure to re-enable device even if error occurs
            try:
                self.connection.enable_device()
            except:
                pass
            return []
    
    def get_users(self) -> List[Dict]:
        """Get users from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return []
        
        try:
            users = self.connection.get_users()
            user_list = []
            
            for user in users:
                user_list.append({
                    'uid': user.uid,
                    'user_id': user.user_id,
                    'name': user.name,
                    'privilege': user.privilege,
                    'password': user.password,
                    'group_id': user.group_id
                })
            
            logger.info(f"Retrieved {len(user_list)} users from ZK device")
            return user_list
            
        except Exception as e:
            logger.error(f"Error getting users: {str(e)}")
            return []
    
    def clear_attendance(self) -> bool:
        """Clear attendance records from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            self.connection.clear_attendance()
            logger.info("Cleared attendance records from ZK device")
            return True
        except Exception as e:
            logger.error(f"Error clearing attendance: {str(e)}")
            return False

    def enroll_user(self, user_id: str, name: str, privilege: int = 0, password: str = '', group_id: str = '0', overwrite: bool = False) -> dict:
        """Enroll a new user in the ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return {'success': False, 'message': 'No connection to ZK device', 'user_exists': False}

        try:
            # Disable device during enrollment
            self.connection.disable_device()

            # Check if user already exists
            existing_users = self.connection.get_users()
            existing_user = None
            for user in existing_users:
                if user.user_id == user_id:
                    existing_user = user
                    break

            if existing_user:
                if not overwrite:
                    logger.warning(f"User {user_id} already exists on device")
                    self.connection.enable_device()
                    return {
                        'success': False,
                        'message': f'User {user_id} already exists on device',
                        'user_exists': True,
                        'existing_user': {
                            'user_id': existing_user.user_id,
                            'name': existing_user.name,
                            'privilege': existing_user.privilege
                        }
                    }
                else:
                    # Delete existing user first
                    logger.info(f"Overwriting existing user {user_id}")
                    self.connection.delete_user(existing_user.uid)

            # Get next available UID
            users = self.connection.get_users()
            max_uid = max([user.uid for user in users] + [0]) + 1

            # Create user object using the correct approach for pyzk
            try:
                # Try different approaches for user creation based on pyzk version
                try:
                    # Method 1: Direct set_user call (newer pyzk versions)
                    self.connection.set_user(
                        uid=int(max_uid),
                        name=str(name),
                        privilege=int(privilege),
                        password=str(password) if password else '',
                        group_id=str(group_id) if group_id else '0',
                        user_id=str(user_id)
                    )
                    logger.info(f"User created with UID {max_uid} using direct set_user")

                except Exception as direct_error:
                    logger.warning(f"Direct set_user failed: {direct_error}")

                    # Method 2: Using User class (older pyzk versions)
                    try:
                        from zk.user import User
                        new_user = User(
                            uid=int(max_uid),
                            name=str(name),
                            privilege=int(privilege),
                            password=str(password) if password else '',
                            group_id=str(group_id) if group_id else '0',
                            user_id=str(user_id)
                        )
                        self.connection.set_user(new_user)
                        logger.info(f"User created with UID {max_uid} using User class")

                    except Exception as class_error:
                        logger.warning(f"User class creation failed: {class_error}")

                        # Method 3: Minimal user creation
                        try:
                            # Some devices only need basic parameters
                            self.connection.set_user(
                                uid=int(max_uid),
                                name=str(name),
                                user_id=str(user_id)
                            )
                            logger.info(f"User created with UID {max_uid} using minimal parameters")
                        except Exception as minimal_error:
                            raise Exception(f"All user creation methods failed: {minimal_error}")

            except Exception as user_error:
                logger.error(f"Failed to create user object: {user_error}")
                # Re-enable device and return error
                self.connection.enable_device()
                return {'success': False, 'message': f'Failed to create user: {str(user_error)}', 'user_exists': False}

            # Re-enable device
            self.connection.enable_device()

            action = "overwritten" if existing_user else "enrolled"
            logger.info(f"Successfully {action} user {user_id} ({name}) on ZK device")
            return {
                'success': True,
                'message': f'User {user_id} successfully {action} on device',
                'user_exists': existing_user is not None,
                'action': action
            }

        except Exception as e:
            logger.error(f"Error enrolling user {user_id}: {str(e)}")
            # Make sure to re-enable device
            try:
                self.connection.enable_device()
            except:
                pass
            return {'success': False, 'message': f'Error enrolling user: {str(e)}', 'user_exists': False}

    def delete_user(self, user_id: str) -> bool:
        """Delete a user from the ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Disable device during deletion
            self.connection.disable_device()

            # Find and delete user
            users = self.connection.get_users()
            for user in users:
                if user.user_id == user_id:
                    self.connection.delete_user(user.uid)
                    logger.info(f"Successfully deleted user {user_id} from ZK device")
                    self.connection.enable_device()
                    return True

            # Re-enable device
            self.connection.enable_device()
            logger.warning(f"User {user_id} not found on device")
            return False

        except Exception as e:
            logger.error(f"Error deleting user {user_id}: {str(e)}")
            # Make sure to re-enable device
            try:
                self.connection.enable_device()
            except:
                pass
            return False

    def start_enrollment_mode(self, user_id: str = None) -> bool:
        """Put device in enrollment mode for biometric capture"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Disable device to prevent normal operation during enrollment
            self.connection.disable_device()

            # Try to trigger enrollment mode using available ZK commands
            try:
                # Some ZK devices support direct enrollment commands
                # This will vary by device model and firmware
                if hasattr(self.connection, 'start_enroll'):
                    # If the device supports start_enroll command
                    if user_id:
                        self.connection.start_enroll(user_id)
                    else:
                        self.connection.start_enroll()
                    logger.info(f"Started enrollment mode for user {user_id if user_id else 'new user'}")
                elif hasattr(self.connection, 'enroll_user'):
                    # Alternative enrollment method
                    logger.info("Using alternative enrollment method")
                else:
                    # Fallback: Use device beep and voice prompts if available
                    if hasattr(self.connection, 'test_voice'):
                        try:
                            self.connection.test_voice()  # Voice prompt for enrollment
                        except:
                            pass

                    # Enable device temporarily to allow manual enrollment
                    self.connection.enable_device()
                    logger.info("Device enabled for manual biometric enrollment")
                    logger.info("Please use the device interface to enroll biometric data")
                    return True

            except Exception as enrollment_error:
                logger.warning(f"Direct enrollment command failed: {enrollment_error}")
                # Fallback to manual enrollment mode
                self.connection.enable_device()
                logger.info("Device enabled for manual biometric enrollment")

            logger.info("Device set to enrollment mode")
            return True

        except Exception as e:
            logger.error(f"Error setting enrollment mode: {str(e)}")
            # Make sure device is re-enabled if error occurs
            try:
                self.connection.enable_device()
            except:
                pass
            return False

    def end_enrollment_mode(self) -> bool:
        """Exit enrollment mode and return to normal operation"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Stop any ongoing enrollment process
            try:
                if hasattr(self.connection, 'cancel_capture'):
                    self.connection.cancel_capture()
                elif hasattr(self.connection, 'stop_enroll'):
                    self.connection.stop_enroll()
            except Exception as stop_error:
                logger.warning(f"Could not stop enrollment process: {stop_error}")

            # Re-enable device for normal operation
            self.connection.enable_device()
            logger.info("Device returned to normal mode")
            return True
        except Exception as e:
            logger.error(f"Error ending enrollment mode: {str(e)}")
            return False

    def trigger_biometric_enrollment(self, user_id: str) -> dict:
        """Trigger biometric enrollment for a specific user"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return {'success': False, 'message': 'No connection to ZK device'}

        try:
            # Disable device during enrollment setup
            self.connection.disable_device()

            # Check if user exists (with retry for newly created users)
            target_user = None
            max_retries = 3

            for attempt in range(max_retries):
                users = self.connection.get_users()
                for user in users:
                    if user.user_id == user_id:
                        target_user = user
                        break

                if target_user:
                    break

                if attempt < max_retries - 1:
                    logger.info(f"User {user_id} not found, retrying... (attempt {attempt + 1}/{max_retries})")
                    import time
                    time.sleep(1)  # Wait 1 second before retry

            if not target_user:
                self.connection.enable_device()
                return {'success': False, 'message': f'User {user_id} not found on device after {max_retries} attempts'}

            # Try different enrollment methods based on device capabilities
            enrollment_started = False

            try:
                # Method 1: Direct enrollment command with user ID
                if hasattr(self.connection, 'start_enroll'):
                    self.connection.start_enroll(target_user.uid)
                    enrollment_started = True
                    logger.info(f"Started enrollment for user {user_id} using start_enroll")

                # Method 2: Alternative enrollment methods
                elif hasattr(self.connection, 'enroll_user'):
                    self.connection.enroll_user(target_user.uid)
                    enrollment_started = True
                    logger.info(f"Started enrollment for user {user_id} using enroll_user")

                # Method 3: Capture finger method
                elif hasattr(self.connection, 'capture_finger'):
                    self.connection.capture_finger()
                    enrollment_started = True
                    logger.info(f"Started finger capture for user {user_id}")

            except Exception as enroll_error:
                logger.warning(f"Direct enrollment methods failed: {enroll_error}")

            if not enrollment_started:
                # Fallback: Enable device and use voice/beep prompts
                try:
                    if hasattr(self.connection, 'test_voice'):
                        self.connection.test_voice()
                    elif hasattr(self.connection, 'beep'):
                        # Beep pattern to indicate enrollment mode
                        for _ in range(3):
                            self.connection.beep()
                except:
                    pass

                self.connection.enable_device()
                logger.info(f"Device enabled for manual enrollment of user {user_id}")
                return {
                    'success': True,
                    'message': f'Device ready for manual biometric enrollment of user {user_id}. Please use device interface.',
                    'manual_mode': True
                }

            # Keep device disabled during enrollment
            return {
                'success': True,
                'message': f'Biometric enrollment started for user {user_id}. Please follow device prompts.',
                'manual_mode': False
            }

        except Exception as e:
            logger.error(f"Error triggering enrollment for user {user_id}: {str(e)}")
            # Make sure device is re-enabled
            try:
                self.connection.enable_device()
            except:
                pass
            return {'success': False, 'message': f'Error triggering enrollment: {str(e)}'}

class AttendanceSync:
    """Synchronize attendance data between ZK device and databases"""
    
    def __init__(self, mysql_config: Optional[Dict] = None):
        self.mysql_config = mysql_config or {
            'host': 'localhost',
            'user': 'root',
            'password': 'yourpass',
            'database': 'staff'
        }
    
    def sync_to_sqlite(self, records: List[Dict], school_id: int = 1) -> int:
        """Sync attendance records to SQLite database"""
        synced_count = 0
        
        try:
            # Use Flask app context to get database connection
            with current_app.app_context():
                db = get_db()
                
                for record in records:
                    try:
                        # Map ZK user_id to staff_id in our system
                        staff = db.execute('''
                            SELECT id FROM staff WHERE staff_id = ? AND school_id = ?
                        ''', (record['user_id'], school_id)).fetchone()
                        
                        if not staff:
                            logger.warning(f"Staff with ID {record['user_id']} not found in school {school_id}")
                            continue
                        
                        staff_id = staff['id']
                        date = record['timestamp'].date()
                        time_val = record['timestamp'].time()
                        
                        # IMPORTANT: Only sync check-in automatically from ZK device
                        # Check-out, overtime-in, and overtime-out must be user-selected
                        if record['punch'] == 0:  # Check In only
                            # Check if record already exists
                            existing = db.execute('''
                                SELECT id FROM attendance
                                WHERE staff_id = ? AND date = ?
                            ''', (staff_id, date)).fetchone()

                            if not existing:
                                # Determine if late (after 9:00 AM)
                                status = 'late' if time_val > datetime.time(9, 0) else 'present'

                                db.execute('''
                                    INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                                    VALUES (?, ?, ?, ?, ?)
                                ''', (staff_id, school_id, date, time_val, status))
                                synced_count += 1

                        # NOTE: Removed automatic check-out sync (punch == 1)
                        # Check-out must be explicitly selected by user through biometric verification
                        # This ensures user has control over when they check-out, overtime-in, overtime-out
                        
                    except Exception as e:
                        logger.error(f"Error syncing record {record}: {str(e)}")
                        continue
                
                db.commit()
                logger.info(f"Synced {synced_count} records to SQLite database")
                
        except Exception as e:
            logger.error(f"Error syncing to SQLite: {str(e)}")
        
        return synced_count
    
    def sync_to_mysql(self, records: List[Dict]) -> int:
        """Sync attendance records to MySQL database"""
        synced_count = 0
        
        try:
            # Connect to MySQL
            conn_db = pymysql.connect(**self.mysql_config)
            cursor = conn_db.cursor()
            
            # Create table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS attendance_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    staff_id VARCHAR(50) NOT NULL,
                    timestamp DATETIME NOT NULL,
                    status INT NOT NULL,
                    punch_type INT NOT NULL,
                    verify_method INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_staff_timestamp (staff_id, timestamp)
                )
            ''')
            
            for record in records:
                try:
                    query = """
                        INSERT INTO attendance_log (staff_id, timestamp, status, punch_type, verify_method)
                        VALUES (%s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        status = VALUES(status),
                        punch_type = VALUES(punch_type),
                        verify_method = VALUES(verify_method)
                    """
                    cursor.execute(query, (
                        record['user_id'],
                        record['timestamp'],
                        record['status'],
                        record['punch'],
                        record['verify']
                    ))
                    synced_count += 1
                    
                except Exception as e:
                    logger.error(f"Error syncing record to MySQL {record}: {str(e)}")
                    continue
            
            conn_db.commit()
            cursor.close()
            conn_db.close()
            
            logger.info(f"Synced {synced_count} records to MySQL database")
            
        except Exception as e:
            logger.error(f"Error syncing to MySQL: {str(e)}")
        
        return synced_count

def sync_attendance_from_device(device_ip: str = '*************', school_id: int = 1) -> Dict:
    """Main function to sync attendance from ZK device"""
    result = {
        'success': False,
        'message': '',
        'sqlite_synced': 0,
        'mysql_synced': 0,
        'total_records': 0
    }
    
    # Initialize ZK device
    zk_device = ZKBiometricDevice(device_ip)
    
    try:
        # Connect to device
        if not zk_device.connect():
            result['message'] = 'Failed to connect to ZK device'
            return result
        
        # Get attendance records
        records = zk_device.get_attendance_records()
        result['total_records'] = len(records)
        
        if not records:
            result['message'] = 'No attendance records found on device'
            result['success'] = True
            return result
        
        # Initialize sync handler
        sync_handler = AttendanceSync()
        
        # Sync to SQLite (main application database)
        result['sqlite_synced'] = sync_handler.sync_to_sqlite(records, school_id)
        
        # Sync to MySQL (backup/reporting database)
        result['mysql_synced'] = sync_handler.sync_to_mysql(records)
        
        result['success'] = True
        result['message'] = f'Successfully synced {result["total_records"]} records'
        
    except Exception as e:
        result['message'] = f'Error during sync: {str(e)}'
        logger.error(f"Sync error: {str(e)}")
    
    finally:
        # Always disconnect
        zk_device.disconnect()
    
    return result

if __name__ == '__main__':
    # Test the ZK device connection
    result = sync_attendance_from_device()
    print(f"Sync result: {result}")
