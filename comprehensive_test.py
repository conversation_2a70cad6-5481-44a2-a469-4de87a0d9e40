#!/usr/bin/env python3
"""
Comprehensive test to verify all buttons and functions are working properly
"""

import requests
import re
import sqlite3
import json
from datetime import datetime, date
from werkzeug.security import generate_password_hash

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def setup_test_environment():
    """Setup test environment with proper credentials"""
    print("🔧 Setting up test environment...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Ensure Mohan <PERSON> has proper password
        password_hash = generate_password_hash('test123')
        cursor.execute("""
            UPDATE staff SET password_hash = ? WHERE staff_id = '888'
        """, (password_hash,))
        
        # Clear today's attendance for fresh testing
        today = date.today()
        cursor.execute("DELETE FROM attendance WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?", (today,))
        
        # Clear today's biometric verifications for fresh testing
        cursor.execute("DELETE FROM biometric_verifications WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND DATE(verification_time) = ?", (today,))
        
        db.commit()
        print("✅ Test environment setup complete")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up test environment: {e}")
        return False
    finally:
        db.close()

def test_admin_login_and_dashboard():
    """Test admin login and dashboard functionality"""
    print("\n📊 Testing Admin Login and Dashboard...")
    
    session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Get CSRF token
        csrf_token = get_csrf_token(session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False, None
        
        # Login as admin
        login_data = {
            'school_id': '4',
            'username': 'admin',
            'password': 'admin',
            'csrf_token': csrf_token
        }
        
        login_response = session.post(f'{base_url}/login', data=login_data)
        if login_response.status_code == 200:
            response_data = login_response.json()
            if response_data.get('redirect'):
                print("✅ Admin login successful")
                
                # Test admin dashboard access
                dashboard_response = session.get(f'{base_url}/admin/dashboard')
                if dashboard_response.status_code == 200:
                    print("✅ Admin dashboard accessible")
                    return True, session
                else:
                    print(f"❌ Admin dashboard not accessible: {dashboard_response.status_code}")
                    return False, None
            else:
                print(f"❌ Login failed: {response_data}")
                return False, None
        else:
            print(f"❌ Login request failed: {login_response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Admin login test error: {e}")
        return False, None

def test_staff_login_and_dashboard():
    """Test staff login and dashboard functionality"""
    print("\n👤 Testing Staff Login and Dashboard...")
    
    session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Get CSRF token
        csrf_token = get_csrf_token(session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False, None
        
        # Login as staff (Mohan Raj)
        login_data = {
            'school_id': '4',
            'username': '888',  # Mohan Raj's staff ID
            'password': 'test123',
            'csrf_token': csrf_token
        }
        
        login_response = session.post(f'{base_url}/login', data=login_data)
        if login_response.status_code == 200:
            response_data = login_response.json()
            if response_data.get('redirect'):
                print("✅ Staff login successful")
                
                # Test staff dashboard access
                dashboard_response = session.get(f'{base_url}/staff/dashboard')
                if dashboard_response.status_code == 200:
                    print("✅ Staff dashboard accessible")
                    return True, session
                else:
                    print(f"❌ Staff dashboard not accessible: {dashboard_response.status_code}")
                    return False, None
            else:
                print(f"❌ Staff login failed: {response_data}")
                return False, None
        else:
            print(f"❌ Staff login request failed: {login_response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Staff login test error: {e}")
        return False, None

def test_biometric_verification_sequence(staff_session):
    """Test the complete biometric verification sequence"""
    print("\n🔐 Testing Biometric Verification Sequence...")
    
    base_url = 'http://127.0.0.1:5000'
    verification_types = ['check-in', 'check-out', 'overtime-in', 'overtime-out']
    
    try:
        # Get CSRF token from staff dashboard
        csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
        if not csrf_token:
            print("❌ Failed to get CSRF token from staff dashboard")
            return False
        
        for i, verification_type in enumerate(verification_types):
            print(f"\n  {i+1}. Testing {verification_type}...")
            
            # Prepare biometric verification data
            verification_data = {
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_type': verification_type,
                'csrf_token': csrf_token
            }
            
            # Submit biometric verification
            response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"     ✅ {verification_type} recorded successfully")
                    print(f"     ⏰ Time: {result.get('time_recorded', 'N/A')}")
                else:
                    print(f"     ❌ {verification_type} failed: {result.get('error', 'Unknown error')}")
                    if verification_type == 'check-out' and 'check-in' in result.get('error', ''):
                        print("     ⚠️  This is expected - check-out requires check-in first")
                    elif verification_type == 'overtime-in' and 'check-out' in result.get('error', ''):
                        print("     ⚠️  This is expected - overtime-in requires check-out first")
                    elif verification_type == 'overtime-out' and 'overtime-in' in result.get('error', ''):
                        print("     ⚠️  This is expected - overtime-out requires overtime-in first")
                    else:
                        return False
            else:
                print(f"     ❌ {verification_type} request failed: {response.status_code}")
                return False
            
            # Small delay between verifications
            import time
            time.sleep(1)
        
        print("✅ All biometric verification types tested")
        return True
        
    except Exception as e:
        print(f"❌ Biometric verification test error: {e}")
        return False

def test_admin_dashboard_updates(admin_session):
    """Test that admin dashboard shows real-time updates"""
    print("\n📈 Testing Admin Dashboard Real-time Updates...")
    
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Test real-time attendance endpoint
        response = admin_session.get(f'{base_url}/get_realtime_attendance')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Real-time attendance endpoint working")
                
                # Check if Mohan Raj's attendance is in the data
                attendance_data = data.get('attendance_data', [])
                mohan_raj_found = False
                
                for record in attendance_data:
                    if record.get('staff_number') == '888':  # Mohan Raj's staff ID
                        mohan_raj_found = True
                        print(f"✅ Mohan Raj found in attendance data:")
                        print(f"     Name: {record.get('full_name')}")
                        print(f"     Check-in: {record.get('time_in', 'Not recorded')}")
                        print(f"     Check-out: {record.get('time_out', 'Not recorded')}")
                        print(f"     Overtime-in: {record.get('overtime_in', 'Not recorded')}")
                        print(f"     Overtime-out: {record.get('overtime_out', 'Not recorded')}")
                        print(f"     Status: {record.get('status', 'Unknown')}")
                        break
                
                if not mohan_raj_found:
                    print("⚠️  Mohan Raj not found in today's attendance data")
                
                # Check summary data
                summary = data.get('summary', {})
                print(f"✅ Attendance Summary:")
                print(f"     Total Staff: {summary.get('total_staff', 0)}")
                print(f"     Present: {summary.get('present', 0)}")
                print(f"     Absent: {summary.get('absent', 0)}")
                print(f"     Late: {summary.get('late', 0)}")
                print(f"     On Leave: {summary.get('on_leave', 0)}")
                
                return True
            else:
                print(f"❌ Real-time attendance endpoint error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Real-time attendance endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Admin dashboard update test error: {e}")
        return False

def test_staff_profile_buttons(admin_session):
    """Test staff profile edit and delete buttons"""
    print("\n🔧 Testing Staff Profile Edit/Delete Buttons...")
    
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Get Mohan Raj's staff ID from database
        db = sqlite3.connect('vishnorex.db')
        cursor = db.cursor()
        cursor.execute("SELECT id FROM staff WHERE staff_id = '888'")
        result = cursor.fetchone()
        db.close()
        
        if not result:
            print("❌ Mohan Raj not found in database")
            return False
        
        staff_db_id = result[0]
        
        # Access staff profile page
        profile_url = f'{base_url}/admin/staff/{staff_db_id}'
        response = admin_session.get(profile_url)
        
        if response.status_code == 200:
            print("✅ Staff profile page accessible")
            
            page_content = response.text
            
            # Check for required elements
            checks = [
                ('editStaffBtn', 'Edit button'),
                ('deleteStaffBtn', 'Delete button'),
                ('staff_profile.js', 'Staff profile JavaScript'),
                ('csrf_token', 'CSRF token'),
                ('editStaffModal', 'Edit modal'),
            ]
            
            for element, description in checks:
                if element in page_content:
                    print(f"✅ {description} found")
                else:
                    print(f"❌ {description} missing")
                    return False
            
            # Test update staff route
            csrf_token = get_csrf_token(admin_session, profile_url)
            if csrf_token:
                update_data = {
                    'staff_id': staff_db_id,
                    'full_name': 'Mohan Raj (Updated)',
                    'email': '<EMAIL>',
                    'phone': '9876543210',
                    'department': 'IT Department',
                    'position': 'Software Developer',
                    'csrf_token': csrf_token
                }
                
                update_response = admin_session.post(f'{base_url}/update_staff', data=update_data)
                if update_response.status_code == 200:
                    result = update_response.json()
                    if result.get('success'):
                        print("✅ Staff update functionality working")
                        
                        # Revert the change
                        revert_data = update_data.copy()
                        revert_data['full_name'] = 'Mohan Raj'
                        admin_session.post(f'{base_url}/update_staff', data=revert_data)
                        
                    else:
                        print(f"❌ Staff update failed: {result.get('error', 'Unknown error')}")
                        return False
                else:
                    print(f"❌ Staff update request failed: {update_response.status_code}")
                    return False
            else:
                print("❌ Failed to get CSRF token for update test")
                return False
            
            return True
        else:
            print(f"❌ Staff profile page not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Staff profile button test error: {e}")
        return False

def check_database_consistency():
    """Check database for consistency after all tests"""
    print("\n🗃️  Checking Database Consistency...")
    
    try:
        db = sqlite3.connect('vishnorex.db')
        cursor = db.cursor()
        
        # Check today's attendance for Mohan Raj
        today = date.today()
        cursor.execute("""
            SELECT date, time_in, time_out, overtime_in, overtime_out, status
            FROM attendance 
            WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') 
            AND date = ?
        """, (today,))
        
        attendance = cursor.fetchone()
        
        if attendance:
            print("✅ Today's attendance record found:")
            print(f"     Date: {attendance[0]}")
            print(f"     Check-in: {attendance[1] or 'Not recorded'}")
            print(f"     Check-out: {attendance[2] or 'Not recorded'}")
            print(f"     Overtime-in: {attendance[3] or 'Not recorded'}")
            print(f"     Overtime-out: {attendance[4] or 'Not recorded'}")
            print(f"     Status: {attendance[5] or 'Unknown'}")
        else:
            print("⚠️  No attendance record found for today")
        
        # Check biometric verifications
        cursor.execute("""
            SELECT verification_type, verification_time, verification_status
            FROM biometric_verifications 
            WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') 
            AND DATE(verification_time) = ?
            ORDER BY verification_time
        """, (today,))
        
        verifications = cursor.fetchall()
        
        if verifications:
            print(f"✅ {len(verifications)} biometric verification(s) found:")
            for i, (v_type, v_time, v_status) in enumerate(verifications, 1):
                print(f"     {i}. {v_type} at {v_time} - {v_status}")
        else:
            print("⚠️  No biometric verifications found for today")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database consistency check error: {e}")
        return False

def main():
    """Run comprehensive test suite"""
    print("🧪 COMPREHENSIVE SYSTEM TEST")
    print("=" * 60)
    
    # Setup test environment
    if not setup_test_environment():
        print("❌ Test environment setup failed")
        return
    
    # Test admin functionality
    admin_success, admin_session = test_admin_login_and_dashboard()
    if not admin_success:
        print("❌ Admin functionality test failed")
        return
    
    # Test staff functionality
    staff_success, staff_session = test_staff_login_and_dashboard()
    if not staff_success:
        print("❌ Staff functionality test failed")
        return
    
    # Test biometric verification sequence
    if not test_biometric_verification_sequence(staff_session):
        print("❌ Biometric verification test failed")
        return
    
    # Test admin dashboard updates
    if not test_admin_dashboard_updates(admin_session):
        print("❌ Admin dashboard update test failed")
        return
    
    # Test staff profile buttons
    if not test_staff_profile_buttons(admin_session):
        print("❌ Staff profile button test failed")
        return
    
    # Check database consistency
    if not check_database_consistency():
        print("❌ Database consistency check failed")
        return
    
    print("\n🎉 ALL TESTS PASSED!")
    print("=" * 60)
    print("✅ Admin login and dashboard working")
    print("✅ Staff login and dashboard working")
    print("✅ Biometric verification sequence working")
    print("✅ Admin dashboard real-time updates working")
    print("✅ Staff profile edit/delete buttons working")
    print("✅ Database consistency maintained")
    print("\n💡 System is fully functional!")

if __name__ == "__main__":
    main()
