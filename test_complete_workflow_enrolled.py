#!/usr/bin/env python3
"""
Test Complete Workflow with Enrolled Staff
Tests check-in, check-out, overtime-in, overtime-out with actually enrolled staff
"""

import requests
import json
import time
from app import app
from database import get_db
import datetime
import re

def test_enrolled_staff_workflow():
    """Test complete workflow with enrolled staff"""
    print("🎯 TESTING COMPLETE WORKFLOW WITH ENROLLED STAFF")
    print("=" * 70)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5000"
    
    # Test with enrolled staff ID 888 (<PERSON>)
    staff_id = "888"
    staff_name = "<PERSON>"
    
    print(f"Testing with: {staff_name} (Staff ID: {staff_id})")
    
    try:
        # Step 1: Get login page and CSRF token
        print("\n1️⃣ Getting login page...")
        response = session.get(f"{base_url}/")
        if response.status_code != 200:
            print(f"❌ Cannot access login page: {response.status_code}")
            return False
        
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', response.text)
        if not csrf_match:
            print("❌ Cannot extract CSRF token")
            return False
        
        csrf_token = csrf_match.group(1)
        print(f"✅ CSRF token: {csrf_token[:20]}...")
        
        # Step 2: Login as enrolled staff
        print("\n2️⃣ Logging in as enrolled staff...")
        login_data = {
            'school_id': '4',
            'username': staff_id,
            'password': 'password123',
            'csrf_token': csrf_token
        }
        
        response = session.post(f"{base_url}/login", data=login_data)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"Login result: {result}")
                
                if result.get('success') or result.get('redirect'):
                    print("✅ Staff login successful")
                    redirect_url = result.get('redirect', '/staff/dashboard')
                else:
                    print(f"❌ Login failed: {result}")
                    return False
            except json.JSONDecodeError:
                # Check if it's a redirect response
                if 'staff/dashboard' in response.text or response.status_code == 302:
                    print("✅ Staff login successful (redirect)")
                    redirect_url = '/staff/dashboard'
                else:
                    print(f"❌ Unexpected login response: {response.text[:200]}")
                    return False
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
        
        # Step 3: Access staff dashboard
        print("\n3️⃣ Accessing staff dashboard...")
        dashboard_response = session.get(f"{base_url}{redirect_url}")
        if dashboard_response.status_code == 200:
            print("✅ Staff dashboard accessible")
            
            # Extract new CSRF token from dashboard
            csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', dashboard_response.text)
            if csrf_match:
                csrf_token = csrf_match.group(1)
                print(f"✅ Dashboard CSRF token: {csrf_token[:20]}...")
            else:
                print("⚠️ No CSRF token in dashboard, using previous token")
        else:
            print(f"❌ Cannot access dashboard: {dashboard_response.status_code}")
            return False
        
        # Step 4: Clear any existing attendance for today
        print("\n4️⃣ Clearing existing attendance...")
        with app.app_context():
            db = get_db()
            today = datetime.date.today()
            
            # Get staff database ID
            staff_record = db.execute('SELECT id FROM staff WHERE staff_id = ?', (staff_id,)).fetchone()
            if not staff_record:
                print(f"❌ Staff {staff_id} not found in database")
                return False
            
            staff_db_id = staff_record['id']
            
            # Clear existing records
            db.execute('DELETE FROM attendance WHERE staff_id = ? AND date = ?', (staff_db_id, today))
            db.execute('DELETE FROM biometric_verifications WHERE staff_id = ? AND DATE(verification_time) = ?', (staff_db_id, today))
            db.commit()
            print(f"✅ Cleared existing attendance for {today}")
        
        # Step 5: Test each verification type
        verification_types = ['check-in', 'check-out', 'overtime-in', 'overtime-out']
        results = {}
        
        for i, verification_type in enumerate(verification_types):
            print(f"\n{5+i}️⃣ Testing {verification_type.upper()}...")
            
            verification_data = {
                'verification_type': verification_type,
                'biometric_method': 'fingerprint',
                'device_ip': '*************',
                'csrf_token': csrf_token
            }
            
            response = session.post(f"{base_url}/biometric_attendance", data=verification_data)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    results[verification_type] = result
                    
                    if result.get('success'):
                        print(f"   ✅ {verification_type} successful!")
                        print(f"   📝 Message: {result.get('message', 'No message')}")
                        if result.get('time_recorded'):
                            print(f"   ⏰ Time: {result['time_recorded']}")
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        if any(keyword in error_msg.lower() for keyword in ['already', 'cannot', 'before', 'after']):
                            print(f"   ⚠️ {verification_type} blocked (expected): {error_msg}")
                        else:
                            print(f"   ❌ {verification_type} failed: {error_msg}")
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON response: {response.text[:100]}")
                    results[verification_type] = {'success': False, 'error': 'Invalid response'}
            else:
                print(f"   ❌ Request failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                results[verification_type] = {'success': False, 'error': f'HTTP {response.status_code}'}
        
        # Step 6: Check final attendance record
        print(f"\n9️⃣ Checking final attendance record...")
        with app.app_context():
            db = get_db()
            
            final_attendance = db.execute('''
                SELECT * FROM attendance WHERE staff_id = ? AND date = ?
            ''', (staff_db_id, today)).fetchone()
            
            if final_attendance:
                print("✅ Final attendance record:")
                print(f"   Check-in: {final_attendance['time_in'] or 'Not recorded'}")
                print(f"   Check-out: {final_attendance['time_out'] or 'Not recorded'}")
                print(f"   Overtime-in: {final_attendance['overtime_in'] or 'Not recorded'}")
                print(f"   Overtime-out: {final_attendance['overtime_out'] or 'Not recorded'}")
                print(f"   Status: {final_attendance['status']}")
            else:
                print("❌ No attendance record found")
            
            # Check biometric verifications
            verifications = db.execute('''
                SELECT verification_type, verification_time, verification_status
                FROM biometric_verifications 
                WHERE staff_id = ? AND DATE(verification_time) = ?
                ORDER BY verification_time
            ''', (staff_db_id, today)).fetchall()
            
            print(f"\n📋 Biometric verifications ({len(verifications)} total):")
            for v in verifications:
                print(f"   {v['verification_type']}: {v['verification_time']} - {v['verification_status']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_results(results):
    """Analyze test results and provide recommendations"""
    print("\n" + "=" * 70)
    print("📊 RESULTS ANALYSIS")
    print("=" * 70)
    
    if not results:
        print("❌ No results to analyze")
        return
    
    successful = [vtype for vtype, result in results.items() if result.get('success')]
    failed = [vtype for vtype, result in results.items() if not result.get('success')]
    
    print(f"✅ Successful verifications: {len(successful)}")
    for vtype in successful:
        print(f"   - {vtype}: {results[vtype].get('message', 'Success')}")
    
    if failed:
        print(f"\n❌ Failed verifications: {len(failed)}")
        for vtype in failed:
            print(f"   - {vtype}: {results[vtype].get('error', 'Unknown error')}")
    
    # Provide specific recommendations
    print("\n💡 RECOMMENDATIONS:")
    
    if 'check-in' in successful:
        print("✅ Check-in is working correctly")
    elif 'check-in' in failed:
        print("🔧 Fix check-in issues first - it's required for other verifications")
    
    if 'check-out' in failed and 'check-in' in successful:
        error = results['check-out'].get('error', '')
        if 'check-in' in error.lower():
            print("🔧 Check-out requires check-in first - this is working correctly")
        else:
            print("🔧 Check-out has unexpected issues")
    
    if any('overtime' in vtype for vtype in failed):
        print("🔧 Overtime verifications may be blocked by time restrictions (before 5 PM)")
    
    if all(vtype in successful for vtype in ['check-in', 'check-out']):
        print("🎉 Basic attendance workflow is working perfectly!")
    
    if len(successful) == 4:
        print("🎉 ALL VERIFICATION TYPES ARE WORKING PERFECTLY!")

def main():
    """Main test function"""
    print("🧪 COMPLETE WORKFLOW TEST WITH ENROLLED STAFF")
    print("=" * 70)
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run the test
    results = test_enrolled_staff_workflow()
    
    # Analyze results
    if results:
        analyze_results(results)
    
    print("\n" + "=" * 70)
    print("🎯 FINAL STATUS")
    print("=" * 70)
    
    if results and any(r.get('success') for r in results.values()):
        print("🎉 ATTENDANCE SYSTEM IS WORKING!")
        print("✅ Biometric verification functional")
        print("✅ Database updates working")
        print("✅ Web interface operational")
        print("\n🔑 LOGIN CREDENTIALS FOR TESTING:")
        print("   School ID: 4")
        print("   Username: 888 (Mohan Raj) or 889 (Raj)")
        print("   Password: password123")
        print("\n🌐 Access: http://127.0.0.1:5000")
    else:
        print("❌ Issues still exist - check the detailed output above")

if __name__ == '__main__':
    main()
