<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edit/Delete Buttons</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Edit/Delete Buttons</h2>
        <p>This page tests if the JavaScript for edit/delete buttons is working correctly.</p>
        
        <!-- Hidden CSRF token for JavaScript -->
        <input type="hidden" name="csrf_token" value="test-csrf-token"/>
        
        <!-- Test elements that match the staff profile page -->
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Staff Profile</h5>
                <div>
                    <button class="btn btn-sm btn-primary me-2" id="editStaffBtn" data-staff-id="5">
                        <i class="bi bi-pencil-square"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-danger" id="deleteStaffBtn" data-staff-id="5">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            </div>
            <div class="card-body">
                <h4 id="staffName">Test Staff</h4>
                <p><strong>Email:</strong> <span id="staffEmail"><EMAIL></span></p>
                <p><strong>Phone:</strong> <span id="staffPhone">************</span></p>
                <p><strong>Department:</strong> <span id="staffDept">IT</span></p>
                <p><strong>Position:</strong> <span id="staffPosition">Developer</span></p>
            </div>
        </div>
        
        <!-- Test Modal -->
        <div class="modal fade" id="editStaffModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Staff</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <input type="hidden" id="editStaffId" value="">
                            <div class="mb-3">
                                <label for="editFullName" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="editFullName">
                            </div>
                            <div class="mb-3">
                                <label for="editEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="editEmail">
                            </div>
                            <div class="mb-3">
                                <label for="editPhone" class="form-label">Phone</label>
                                <input type="text" class="form-control" id="editPhone">
                            </div>
                            <div class="mb-3">
                                <label for="editDepartment" class="form-label">Department</label>
                                <input type="text" class="form-control" id="editDepartment">
                            </div>
                            <div class="mb-3">
                                <label for="editPosition" class="form-label">Position</label>
                                <input type="text" class="form-control" id="editPosition">
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="editStatus">
                                <label class="form-check-label" for="editStatus">Active</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveEditStaff">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h5>Test Results:</h5>
            <div id="testResults">
                <p>Click the Edit or Delete buttons above to test functionality.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../static/js/staff_profile.js"></script>
    
    <script>
        // Override fetch to show test results instead of making real API calls
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            const testResults = document.getElementById('testResults');
            
            if (url === '/delete_staff') {
                testResults.innerHTML = `
                    <div class="alert alert-success">
                        ✅ Delete button clicked successfully!<br>
                        URL: ${url}<br>
                        Method: ${options.method}<br>
                        Body: ${options.body}
                    </div>
                `;
                // Return a mock successful response
                return Promise.resolve({
                    json: () => Promise.resolve({ success: true })
                });
            } else if (url === '/update_staff') {
                testResults.innerHTML = `
                    <div class="alert alert-success">
                        ✅ Edit/Update button clicked successfully!<br>
                        URL: ${url}<br>
                        Method: ${options.method}
                    </div>
                `;
                // Return a mock successful response
                return Promise.resolve({
                    json: () => Promise.resolve({ success: true })
                });
            }
            
            // For other requests, use original fetch
            return originalFetch.apply(this, arguments);
        };
        
        // Test if JavaScript loaded
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            testResults.innerHTML += '<div class="alert alert-info">✅ JavaScript loaded successfully!</div>';
        });
    </script>
</body>
</html>
