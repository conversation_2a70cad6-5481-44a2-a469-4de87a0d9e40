import sqlite3

db = sqlite3.connect('vishnorex.db')
cursor = db.cursor()

print("=== ADMINS ===")
cursor.execute('SELECT id, school_id, username FROM admins')
admins = cursor.fetchall()
for admin in admins:
    print(f"ID: {admin[0]}, School: {admin[1]}, Username: {admin[2]}")

print("\n=== SCHOOLS ===")
cursor.execute('SELECT id, name FROM schools')
schools = cursor.fetchall()
for school in schools:
    print(f"ID: {school[0]}, Name: {school[1]}")

print("\n=== STAFF ===")
cursor.execute('SELECT id, school_id, staff_id, full_name FROM staff')
staff = cursor.fetchall()
for s in staff:
    print(f"ID: {s[0]}, School: {s[1]}, Staff ID: {s[2]}, Name: {s[3]}")

db.close()
