#!/usr/bin/env python3
"""
Fix staff passwords - set default passwords for staff without passwords
"""

from app import app
from database import get_db
from werkzeug.security import generate_password_hash

def fix_staff_passwords():
    """Set default passwords for staff who don't have passwords"""
    print("🔧 FIXING STAFF PASSWORDS")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Find staff without passwords
        staff_without_passwords = db.execute('''
            SELECT id, staff_id, full_name, school_id 
            FROM staff 
            WHERE password_hash IS NULL OR password_hash = ''
        ''').fetchall()
        
        if not staff_without_passwords:
            print("✅ All staff already have passwords")
            return
        
        print(f"Found {len(staff_without_passwords)} staff without passwords:")
        
        for staff in staff_without_passwords:
            # Set default password as "password123"
            default_password = "password123"
            password_hash = generate_password_hash(default_password)
            
            db.execute('''
                UPDATE staff 
                SET password_hash = ? 
                WHERE id = ?
            ''', (password_hash, staff['id']))
            
            print(f"  ✅ {staff['full_name']} (ID: {staff['staff_id']}) - Password set to: {default_password}")
        
        db.commit()
        print(f"\n✅ Updated {len(staff_without_passwords)} staff passwords")
        print("Default password for all staff: password123")

if __name__ == '__main__':
    fix_staff_passwords()
