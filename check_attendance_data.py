#!/usr/bin/env python3
"""
Check attendance data in the database
"""

from app import app
from database import get_db
import datetime

def check_attendance_data():
    with app.app_context():
        db = get_db()
        
        # Check if there are any attendance records for today
        today = datetime.date.today()
        print(f'Checking attendance for {today}')
        
        # Check total attendance records
        total_records = db.execute('SELECT COUNT(*) as count FROM attendance').fetchone()
        print(f'Total attendance records in database: {total_records["count"]}')
        
        # Check today's attendance
        today_records = db.execute('SELECT COUNT(*) as count FROM attendance WHERE date = ?', (today,)).fetchone()
        print(f'Today\'s attendance records: {today_records["count"]}')
        
        # Check recent attendance records
        recent = db.execute('SELECT * FROM attendance ORDER BY date DESC LIMIT 5').fetchall()
        print(f'Recent attendance records:')
        for record in recent:
            print(f'  Staff ID: {record["staff_id"]}, Date: {record["date"]}, Time In: {record["time_in"]}, Status: {record["status"]}')
        
        # Check staff count
        staff_count = db.execute('SELECT COUNT(*) as count FROM staff WHERE school_id = 4').fetchone()
        print(f'Total staff in school 4: {staff_count["count"]}')
        
        # Check if there are any staff with matching IDs from the biometric device
        print('\nChecking for staff with IDs that match biometric device records...')
        
        # Some sample IDs from the warnings we saw
        sample_ids = ['584', '889', '4523', '7078', '9999', '1302', '1304', '8003']
        
        for staff_id in sample_ids:
            staff = db.execute('SELECT * FROM staff WHERE staff_id = ? AND school_id = 4', (staff_id,)).fetchone()
            if staff:
                print(f'  Found staff: ID {staff_id} - {staff["full_name"]}')
            else:
                print(f'  Staff ID {staff_id} not found in database')

if __name__ == '__main__':
    check_attendance_data()
