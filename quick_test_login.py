#!/usr/bin/env python3
"""
Quick test for staff login and biometric verification
"""

import requests
import re
import json
import sqlite3
from werkzeug.security import generate_password_hash

def setup_staff_password():
    """Set up password for <PERSON> staff member"""
    print("🔧 Setting up staff password...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Set password for <PERSON> (staff_id: 888, school_id: 4)
        password_hash = generate_password_hash('test123')
        cursor.execute("""
            UPDATE staff SET password_hash = ? WHERE staff_id = '888'
        """, (password_hash,))
        
        db.commit()
        print("✅ Password set for staff member 888 (<PERSON>)")
        return True
        
    except Exception as e:
        print(f"❌ Error setting password: {e}")
        return False
    finally:
        db.close()

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def test_complete_flow():
    """Test the complete flow"""
    print("🚀 Testing Complete Biometric Verification Flow")
    print("=" * 50)
    
    # Setup password
    if not setup_staff_password():
        return False
    
    session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Step 1: Get CSRF token
        print("\n1️⃣ Getting CSRF token...")
        csrf_token = get_csrf_token(session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        print(f"✅ Got CSRF token")
        
        # Step 2: Login as staff
        print("\n2️⃣ Logging in as staff...")
        login_data = {
            'school_id': '4',  # Bharathiyar school
            'username': '888',  # Mohan Raj's staff ID
            'password': 'test123',
            'csrf_token': csrf_token
        }
        
        login_response = session.post(f'{base_url}/login', data=login_data)
        print(f"📡 Login status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            result = login_response.json()
            if 'redirect' in result and 'staff/dashboard' in result['redirect']:
                print("✅ Staff login successful!")
            else:
                print(f"❌ Login failed: {result}")
                return False
        else:
            print(f"❌ Login request failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
        
        # Step 3: Get staff dashboard CSRF token
        print("\n3️⃣ Getting staff dashboard...")
        dashboard_response = session.get(f'{base_url}/staff/dashboard')
        if dashboard_response.status_code != 200:
            print(f"❌ Failed to access staff dashboard: {dashboard_response.status_code}")
            return False
        
        csrf_token = get_csrf_token(session, f'{base_url}/staff/dashboard')
        if not csrf_token:
            print("❌ Failed to get dashboard CSRF token")
            return False
        print("✅ Got dashboard CSRF token")
        
        # Step 4: Test biometric verification
        print("\n4️⃣ Testing biometric verification...")
        verification_data = {
            'verification_type': 'check-in',
            'biometric_method': 'fingerprint',
            'csrf_token': csrf_token
        }
        
        verification_response = session.post(f'{base_url}/biometric_attendance', data=verification_data)
        print(f"📡 Verification status: {verification_response.status_code}")
        
        if verification_response.status_code == 200:
            result = verification_response.json()
            print(f"📄 Verification result: {result}")
            
            if result.get('success'):
                print("🎉 BIOMETRIC VERIFICATION SUCCESSFUL!")
                print(f"✅ Message: {result.get('message')}")
                print(f"✅ Time recorded: {result.get('time_recorded')}")
                print(f"✅ Verification type: {result.get('verification_type')}")
                return True
            else:
                print(f"❌ Verification failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Verification request failed: {verification_response.status_code}")
            print(f"Response: {verification_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def check_database_after_test():
    """Check if the verification was recorded in database"""
    print("\n5️⃣ Checking database records...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Check attendance record
        cursor.execute("""
            SELECT a.*, s.full_name 
            FROM attendance a 
            JOIN staff s ON a.staff_id = s.id 
            WHERE s.staff_id = '888' AND a.date = date('now')
        """)
        attendance = cursor.fetchone()
        
        if attendance:
            print(f"✅ Attendance record found:")
            print(f"   Staff: {attendance[-1]}")
            print(f"   Date: {attendance[3]}")
            print(f"   Time in: {attendance[4]}")
            print(f"   Status: {attendance[6]}")
        else:
            print("❌ No attendance record found")
        
        # Check biometric verification log
        cursor.execute("""
            SELECT bv.*, s.full_name 
            FROM biometric_verifications bv 
            JOIN staff s ON bv.staff_id = s.id 
            WHERE s.staff_id = '888' AND date(bv.verification_time) = date('now')
        """)
        verification = cursor.fetchone()
        
        if verification:
            print(f"✅ Biometric verification log found:")
            print(f"   Staff: {verification[-1]}")
            print(f"   Type: {verification[3]}")
            print(f"   Time: {verification[4]}")
            print(f"   Status: {verification[7]}")
        else:
            print("❌ No biometric verification log found")
            
        return attendance is not None and verification is not None
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_complete_flow()
    
    if success:
        db_success = check_database_after_test()
        
        print("\n" + "=" * 50)
        if db_success:
            print("🎉 COMPLETE SUCCESS!")
            print("✅ Staff login working")
            print("✅ Biometric verification working")
            print("✅ Database records created")
            print("✅ Admin dashboard should show the data")
            print("\n💡 Next steps:")
            print("   1. Open admin dashboard: http://127.0.0.1:5000")
            print("   2. Login as admin")
            print("   3. Check today's attendance table")
            print("   4. You should see Mohan Raj's check-in time!")
        else:
            print("⚠️  Verification succeeded but database records missing")
    else:
        print("\n" + "=" * 50)
        print("❌ TEST FAILED")
        print("💡 Please check the error messages above")
