from app import app
from database import get_db

with app.app_context():
    db = get_db()
    
    # Check if biometric_verifications table exists
    tables = db.execute('SELECT name FROM sqlite_master WHERE type="table"').fetchall()
    print('Database tables:')
    for table in tables:
        print(f'  - {table["name"]}')
    
    print()
    
    # Check biometric_verifications table structure
    try:
        columns = db.execute('PRAGMA table_info(biometric_verifications)').fetchall()
        print('biometric_verifications table columns:')
        for col in columns:
            print(f'  - {col["name"]} ({col["type"]})')
    except Exception as e:
        print(f'biometric_verifications table does not exist: {e}')
    
    print()
    
    # Check attendance table structure
    try:
        columns = db.execute('PRAGMA table_info(attendance)').fetchall()
        print('attendance table columns:')
        for col in columns:
            print(f'  - {col["name"]} ({col["type"]})')
    except Exception as e:
        print(f'attendance table error: {e}')
    
    print()
    
    # Check staff table structure
    try:
        columns = db.execute('PRAGMA table_info(staff)').fetchall()
        print('staff table columns:')
        for col in columns:
            print(f'  - {col["name"]} ({col["type"]})')
    except Exception as e:
        print(f'staff table error: {e}')
