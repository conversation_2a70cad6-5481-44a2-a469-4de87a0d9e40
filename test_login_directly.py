from app import app
from database import get_db
from werkzeug.security import check_password_hash

with app.app_context():
    db = get_db()
    
    # Test login logic directly
    school_id = '4'
    username = 'admin1'
    password = 'admin123'
    
    print(f"Testing login: School {school_id}, Username: {username}")
    
    # Check school admin
    admin = db.execute('''
        SELECT * FROM admins 
        WHERE school_id = ? AND username = ?
    ''', (school_id, username)).fetchone()
    
    if admin:
        print(f"Admin found: {admin['full_name']}")
        print(f"Password hash: {admin['password'][:20]}...")
        
        # Test different passwords
        test_passwords = ['admin123', 'password', 'admin', '123456', 'password123']
        
        for test_pass in test_passwords:
            if check_password_hash(admin['password'], test_pass):
                print(f"✅ Correct password: {test_pass}")
                break
        else:
            print("❌ None of the test passwords worked")
            
            # Let's see what the actual password hash looks like
            print(f"Full password hash: {admin['password']}")
    else:
        print("❌ Admin not found")
        
        # Check what admins exist
        all_admins = db.execute('SELECT school_id, username, full_name FROM admins').fetchall()
        print("Available admins:")
        for a in all_admins:
            print(f"  School {a['school_id']}: {a['username']} ({a['full_name']})")
