document.addEventListener('DOMContentLoaded', function() {
    // Helper function to get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('input[name="csrf_token"]');
        return token ? token.value : '';
    }
    // Initialize attendance chart with real data
    const ctx = document.getElementById('attendanceChart')?.getContext('2d');
    if (ctx) {
        fetch('/get_attendance_summary')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const attendanceChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Present', 'Absent', 'Late', 'Leave'],
                            datasets: [{
                                data: [data.present, data.absent, data.late, data.leave],
                                backgroundColor: [
                                    '#198754',
                                    '#dc3545',
                                    '#ffc107',
                                    '#0dcaf0'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });

                    // Update counts
                    document.getElementById('presentDays').textContent = data.present;
                    document.getElementById('absentDays').textContent = data.absent;
                    document.getElementById('lateDays').textContent = data.late;
                    document.getElementById('leaveDays').textContent = data.leave;
                }
            });
    }

    // Biometric authentication
    const startAuthBtn = document.getElementById('startAuthBtn');
    const fingerprintScanner = document.getElementById('fingerprintScanner');
    const faceScanner = document.getElementById('faceScanner');
    const authStatus = document.getElementById('authStatus');
    const attendanceStatus = document.getElementById('attendanceStatus');
    const markInBtn = document.getElementById('markInBtn');
    const markOutBtn = document.getElementById('markOutBtn');

    let authStep = 0; // 0: not started, 1: fingerprint, 2: face

    // Removed duplicate event listener - using the one at line 190

    function fingerprintScan() {
        authStatus.innerHTML = '<div class="alert alert-info">Scanning fingerprint... Please place your finger on the scanner</div>';

        setTimeout(() => {
            authStatus.innerHTML = '<div class="alert alert-success">Fingerprint verified successfully</div>';
            fingerprintScanner.style.display = 'none';
            faceScanner.style.display = 'block';
            authStep = 2;
            faceRecognition();
        }, 2000);
    }

    function faceRecognition() {
        authStatus.innerHTML = '<div class="alert alert-info">Starting face recognition... Please look at the camera</div>';

        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                    const video = document.getElementById('faceVideo');
                    video.srcObject = stream;

                    setTimeout(() => {
                        const canvas = document.getElementById('canvasElement');
                        const context = canvas.getContext('2d');
                        context.drawImage(video, 0, 0, canvas.width, canvas.height);

                        stream.getTracks().forEach(track => track.stop());
                        video.srcObject = null;

                        completeAuthentication();
                    }, 3000);
                });
        }
    }

    function completeAuthentication() {
        authStatus.innerHTML = '<div class="alert alert-success">Biometric authentication successful!</div>';
        faceScanner.style.display = 'none';
        attendanceStatus.innerHTML = '<h4>Status: <span class="text-success">Ready to Mark Attendance</span></h4>';
        markInBtn.disabled = false;
    }

    // Initialize calendar
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: function (fetchInfo, successCallback, failureCallback) {
                fetch(`/get_staff_attendance?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                    .then(response => response.json())
                    .then(data => {
                        const events = data.attendance.map(record => {
                            let color;
                            if (record.status === 'present') color = '#198754';
                            else if (record.status === 'absent') color = '#dc3545';
                            else if (record.status === 'late') color = '#ffc107';
                            else if (record.status === 'leave') color = '#0dcaf0';

                            return {
                                title: `${record.status}${record.time_in ? ' (' + record.time_in + ')' : ''}`,
                                start: record.date,
                                allDay: true,
                                backgroundColor: color,
                                borderColor: color
                            };
                        });

                        if (data.holidays) {
                            data.holidays.forEach(holiday => {
                                events.push({
                                    title: holiday.name,
                                    start: holiday.date,
                                    allDay: true,
                                    backgroundColor: '#6c757d',
                                    borderColor: '#6c757d'
                                });
                            });
                        }

                        successCallback(events);
                    })
                    .catch(error => {
                        console.error('Error fetching attendance:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function (info) {
                alert('Attendance: ' + info.event.title);
            }
        });
        calendar.render();
    }

    // Global variables for biometric verification
    let isVerificationInProgress = false;

    // Load today's attendance status on page load
    loadTodayAttendanceStatus();

    // Handle verification type selection
    document.querySelectorAll('input[name="verificationType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const selectedAction = document.getElementById('selectedAction');
            const selectedType = this.value;
            const typeLabels = {
                'check-in': 'Check-in',
                'check-out': 'Check-out',
                'overtime-in': 'Overtime-in',
                'overtime-out': 'Overtime-out'
            };
            selectedAction.innerHTML = `Selected: <span class="text-primary">${typeLabels[selectedType]}</span>`;
        });
    });

    // Start authentication button
    document.getElementById('startAuthBtn')?.addEventListener('click', function() {
        if (isVerificationInProgress) return;

        performBiometricVerification();
    });

    function loadTodayAttendanceStatus() {
        fetch('/get_today_attendance_status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateAttendanceDisplay(data.attendance);
                    updateVerificationHistory(data.verifications);
                    updateAvailableActions(data.available_actions);
                }
            })
            .catch(error => {
                console.error('Error loading attendance status:', error);
            });
    }

    function updateAttendanceDisplay(attendance) {
        const currentStatus = document.getElementById('currentStatus');
        const timeIn = document.getElementById('timeIn');
        const timeOut = document.getElementById('timeOut');
        const overtimeIn = document.getElementById('overtimeIn');
        const overtimeOut = document.getElementById('overtimeOut');

        if (attendance) {
            timeIn.textContent = attendance.time_in || '--:--:--';
            timeOut.textContent = attendance.time_out || '--:--:--';
            overtimeIn.textContent = attendance.overtime_in || '--:--:--';
            overtimeOut.textContent = attendance.overtime_out || '--:--:--';

            // Update status based on attendance
            if (attendance.overtime_out) {
                currentStatus.textContent = 'All Complete';
                currentStatus.className = 'text-success';
            } else if (attendance.overtime_in) {
                currentStatus.textContent = 'Overtime In Progress';
                currentStatus.className = 'text-warning';
            } else if (attendance.time_out) {
                currentStatus.textContent = 'Regular Hours Complete';
                currentStatus.className = 'text-info';
            } else if (attendance.time_in) {
                currentStatus.textContent = 'Checked In';
                currentStatus.className = 'text-primary';
            } else {
                currentStatus.textContent = 'Not Marked';
                currentStatus.className = 'text-secondary';
            }
        } else {
            currentStatus.textContent = 'Not Marked';
            currentStatus.className = 'text-secondary';
            timeIn.textContent = '--:--:--';
            timeOut.textContent = '--:--:--';
            overtimeIn.textContent = '--:--:--';
            overtimeOut.textContent = '--:--:--';
        }
    }

    function updateVerificationHistory(verifications) {
        const historyBody = document.getElementById('verificationHistory');

        if (verifications.length === 0) {
            historyBody.innerHTML = '<tr><td colspan="4" class="text-center">No verifications today</td></tr>';
            return;
        }

        historyBody.innerHTML = verifications.map(v => {
            const time = new Date(v.verification_time).toLocaleTimeString();
            const statusBadge = v.verification_status === 'success' ?
                '<span class="badge bg-success">Success</span>' :
                '<span class="badge bg-danger">Failed</span>';

            return `
                <tr>
                    <td>${time}</td>
                    <td>${v.verification_type}</td>
                    <td>${v.biometric_method}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        }).join('');
    }

    function updateAvailableActions(availableActions) {
        const authBtn = document.getElementById('startAuthBtn');
        const radioButtons = document.querySelectorAll('input[name="verificationType"]');

        // Enable/disable radio buttons based on available actions
        radioButtons.forEach(radio => {
            const isAvailable = availableActions.includes(radio.value);
            radio.disabled = !isAvailable;

            // Update label styling
            const label = radio.nextElementSibling;
            if (isAvailable) {
                label.classList.remove('text-muted');
                label.style.opacity = '1';
            } else {
                label.classList.add('text-muted');
                label.style.opacity = '0.5';
            }
        });

        // Select the first available action
        if (availableActions.length > 0) {
            const firstAvailable = document.querySelector(`input[name="verificationType"][value="${availableActions[0]}"]`);
            if (firstAvailable) {
                firstAvailable.checked = true;
                // Trigger change event to update selected action display
                firstAvailable.dispatchEvent(new Event('change'));
            }
        }

        // Enable/disable the verification button based on completion status
        if (availableActions.length === 0) {
            authBtn.disabled = true;
            authBtn.innerHTML = '<i class="bi bi-check-circle"></i> All Complete';
        } else {
            authBtn.disabled = false;
            authBtn.innerHTML = '<i class="bi bi-fingerprint"></i> Verify Biometric';
        }
    }

    function performBiometricVerification() {
        isVerificationInProgress = true;

        const authBtn = document.getElementById('startAuthBtn');
        const authStatus = document.getElementById('authStatus');

        // Get selected verification type
        const selectedType = document.querySelector('input[name="verificationType"]:checked').value;

        // Update UI
        authBtn.disabled = true;
        authBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Verifying...';

        authStatus.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-fingerprint"></i> Biometric verification in progress...<br>
                Please keep your finger on the scanner.<br>
                <strong>Type:</strong> ${selectedType.replace('-', ' ').toUpperCase()}
            </div>
        `;

        // Perform biometric verification with selected type
        fetch('/biometric_attendance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `verification_type=${encodeURIComponent(selectedType)}&biometric_method=fingerprint&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                authStatus.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> ${data.message}<br>
                        <small><strong>Type:</strong> ${data.verification_type}</small><br>
                        <small><strong>Time:</strong> ${data.time_recorded}</small><br>
                        <small><strong>Verified:</strong> ${data.verification_time}</small>
                    </div>
                `;

                // Reload attendance status
                setTimeout(() => {
                    loadTodayAttendanceStatus();
                }, 1000);

            } else {
                authStatus.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle"></i> ${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            authStatus.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle"></i> Verification failed: ${error.message}
                </div>
            `;
        })
        .finally(() => {
            isVerificationInProgress = false;
            authBtn.disabled = false;
            authBtn.innerHTML = '<i class="bi bi-fingerprint"></i> Verify Biometric';
        });
    }

    // Apply leave
    const submitLeave = document.getElementById('submitLeave');
    submitLeave?.addEventListener('click', function () {
        const leaveType = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reason = document.getElementById('leaveReason').value;

        if (!leaveType || !startDate || !endDate || !reason) {
            alert('Please fill all fields');
            return;
        }

        fetch('/apply_leave', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `leave_type=${leaveType}&start_date=${startDate}&end_date=${endDate}&reason=${encodeURIComponent(reason)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Leave application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyLeaveModal')).hide();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit leave application');
                }
            });
    });

    // Updated download report with date selection
// Updated download report with date selection
document.getElementById('downloadReportBtn')?.addEventListener('click', function() {
    const startDate = prompt('Enter start date (YYYY-MM-DD):');
    if (!startDate) return;

    const endDate = prompt('Enter end date (YYYY-MM-DD):');
    if (!endDate) return;

    fetch(`/export_staff_report?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_report_${startDate}_to_${endDate}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        });
});
});
