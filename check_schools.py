import sqlite3

db = sqlite3.connect('vishnorex.db')
cursor = db.cursor()

print("Schools:")
cursor.execute('SELECT id, name FROM schools')
schools = cursor.fetchall()
for school in schools:
    print(f"  ID: {school[0]}, Name: {school[1]}")

print("\nStaff with school IDs:")
cursor.execute('SELECT id, staff_id, full_name, school_id FROM staff')
staff = cursor.fetchall()
for s in staff:
    print(f"  Staff ID: {s[1]}, Name: {s[2]}, School ID: {s[3]}")

db.close()
