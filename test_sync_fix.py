#!/usr/bin/env python3
"""
Test script to verify the biometric sync fixes
"""

import sys
import json
from datetime import datetime
from app import app
from zk_biometric import sync_attendance_from_device

def test_sync_with_app_context():
    """Test sync within Flask app context"""
    print("🔧 Testing biometric sync fixes...")
    print("=" * 50)
    
    with app.app_context():
        try:
            print("📡 Connecting to ZK device and syncing attendance...")
            result = sync_attendance_from_device('*************', 4)
            
            print("\n📊 Sync Results:")
            print(f"   Success: {result['success']}")
            print(f"   Message: {result['message']}")
            print(f"   Total Records: {result['total_records']}")
            print(f"   SQLite Synced: {result['sqlite_synced']}")
            print(f"   MySQL Synced: {result['mysql_synced']}")
            
            if result['success']:
                print("\n✅ Sync completed successfully!")
                if result['sqlite_synced'] > 0:
                    print(f"   ✅ {result['sqlite_synced']} records synced to SQLite")
                else:
                    print("   ℹ️  No new records to sync to SQLite")
                    
                if result['mysql_synced'] > 0:
                    print(f"   ✅ {result['mysql_synced']} records synced to MySQL")
                else:
                    print("   ⚠️  MySQL sync failed or no new records")
            else:
                print(f"\n❌ Sync failed: {result['message']}")
                return False
                
        except Exception as e:
            print(f"\n❌ Error during sync test: {str(e)}")
            return False
    
    return True

def test_database_time_handling():
    """Test that time values are properly handled in database operations"""
    print("\n🕒 Testing database time handling...")
    print("=" * 50)
    
    with app.app_context():
        from database import get_db
        import datetime
        
        try:
            db = get_db()
            
            # Test inserting a time value as string
            test_time = datetime.datetime.now().time().strftime('%H:%M:%S')
            test_date = datetime.date.today()
            
            print(f"   Testing time format: {test_time}")
            print(f"   Testing date format: {test_date}")
            
            # Try to insert a test record (we'll delete it after)
            cursor = db.execute('''
                SELECT id FROM staff WHERE school_id = 4 LIMIT 1
            ''')
            staff_record = cursor.fetchone()
            
            if staff_record:
                staff_id = staff_record['id']
                
                # Check if attendance record already exists
                existing = db.execute('''
                    SELECT id FROM attendance WHERE staff_id = ? AND date = ?
                ''', (staff_id, test_date)).fetchone()
                
                if not existing:
                    # Insert test record
                    db.execute('''
                        INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (staff_id, 4, test_date, test_time, 'present'))
                    
                    print("   ✅ Successfully inserted time value as string")
                    
                    # Clean up test record
                    db.execute('''
                        DELETE FROM attendance WHERE staff_id = ? AND date = ?
                    ''', (staff_id, test_date))
                    
                    print("   ✅ Test record cleaned up")
                else:
                    print("   ℹ️  Attendance record already exists for today")
                
                db.commit()
                print("   ✅ Database time handling test passed")
                return True
            else:
                print("   ⚠️  No staff records found for testing")
                return False
                
        except Exception as e:
            print(f"   ❌ Database time handling test failed: {str(e)}")
            return False

def main():
    """Main test function"""
    print("🧪 Biometric Sync Fix Test Suite")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Database time handling
    if test_database_time_handling():
        tests_passed += 1
    
    # Test 2: Sync with app context
    if test_sync_with_app_context():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The sync fixes are working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
