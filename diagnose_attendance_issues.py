#!/usr/bin/env python3
"""
Comprehensive Attendance System Diagnostics
Identifies specific issues with check-in, check-out, overtime-in, overtime-out
"""

import requests
import json
import time
from app import app
from database import get_db
import datetime

def check_database_schema():
    """Check if attendance table has all required columns"""
    print("🗄️ CHECKING DATABASE SCHEMA")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Check attendance table structure
        columns = db.execute('PRAGMA table_info(attendance)').fetchall()
        required_columns = ['time_in', 'time_out', 'overtime_in', 'overtime_out', 'status']
        
        print("Attendance table columns:")
        existing_columns = []
        for col in columns:
            print(f"  - {col['name']} ({col['type']})")
            existing_columns.append(col['name'])
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present")
            return True

def check_biometric_verifications_table():
    """Check biometric_verifications table"""
    print("\n🔐 CHECKING BIOMETRIC VERIFICATIONS TABLE")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        try:
            columns = db.execute('PRAGMA table_info(biometric_verifications)').fetchall()
            print("Biometric verifications table columns:")
            for col in columns:
                print(f"  - {col['name']} ({col['type']})")
            
            # Check recent verifications
            recent = db.execute('''
                SELECT verification_type, verification_status, COUNT(*) as count
                FROM biometric_verifications 
                WHERE DATE(verification_time) = DATE('now')
                GROUP BY verification_type, verification_status
            ''').fetchall()
            
            print("\nToday's verification attempts:")
            for r in recent:
                print(f"  {r['verification_type']}: {r['count']} {r['verification_status']}")
            
            return True
        except Exception as e:
            print(f"❌ Error checking biometric_verifications table: {e}")
            return False

def check_staff_dashboard_access():
    """Check if staff can access the dashboard"""
    print("\n👤 CHECKING STAFF DASHBOARD ACCESS")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Get a test staff member
        staff = db.execute('SELECT * FROM staff WHERE school_id = 4 LIMIT 1').fetchone()
        if not staff:
            print("❌ No staff found for testing")
            return False
        
        print(f"Test staff: {staff['full_name']} (ID: {staff['id']})")
        print(f"Staff ID for login: {staff['staff_id']}")
        
        # Check if staff has password
        if staff['password_hash']:
            print("✅ Staff has password set")
        else:
            print("❌ Staff has no password - cannot login")
            return False
        
        return True

def check_verification_route():
    """Check if the biometric_attendance route is working"""
    print("\n🔗 CHECKING VERIFICATION ROUTE")
    print("=" * 50)
    
    # Test the route directly
    with app.test_client() as client:
        with app.app_context():
            db = get_db()
            
            # Get a staff member
            staff = db.execute('SELECT * FROM staff WHERE school_id = 4 LIMIT 1').fetchone()
            if not staff:
                print("❌ No staff found")
                return False
            
            # Simulate staff login session
            with client.session_transaction() as sess:
                sess['user_id'] = staff['id']
                sess['school_id'] = staff['school_id']
                sess['user_type'] = 'staff'
                sess['full_name'] = staff['full_name']
            
            # Test check-in request
            response = client.post('/biometric_attendance', data={
                'verification_type': 'check-in',
                'biometric_method': 'fingerprint',
                'device_ip': '*************'
            })
            
            print(f"Route response status: {response.status_code}")
            if response.status_code == 200:
                data = response.get_json()
                if data:
                    print(f"Response: {data}")
                    if data.get('success'):
                        print("✅ Route working correctly")
                        return True
                    else:
                        print(f"❌ Route returned error: {data.get('error')}")
                        return False
                else:
                    print("❌ No JSON response")
                    return False
            else:
                print(f"❌ Route returned status {response.status_code}")
                return False

def check_javascript_functionality():
    """Check if JavaScript files are properly structured"""
    print("\n📜 CHECKING JAVASCRIPT FUNCTIONALITY")
    print("=" * 50)
    
    try:
        with open('static/js/staff_dashboard.js', 'r') as f:
            js_content = f.read()
        
        # Check for key functions
        required_functions = [
            'startBiometricAuth',
            'loadTodayAttendanceStatus',
            'updateAvailableActions'
        ]
        
        for func in required_functions:
            if func in js_content:
                print(f"✅ Function {func} found")
            else:
                print(f"❌ Function {func} missing")
        
        # Check for verification type handling
        if 'verificationType' in js_content:
            print("✅ Verification type handling found")
        else:
            print("❌ Verification type handling missing")
        
        return True
    except Exception as e:
        print(f"❌ Error checking JavaScript: {e}")
        return False

def check_current_attendance_status():
    """Check current attendance status for all staff"""
    print("\n📊 CHECKING CURRENT ATTENDANCE STATUS")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        today = datetime.date.today()
        attendance_records = db.execute('''
            SELECT s.full_name, s.staff_id, a.*
            FROM staff s
            LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
            WHERE s.school_id = 4
            ORDER BY s.full_name
        ''', (today,)).fetchall()
        
        print(f"Attendance status for {today}:")
        for record in attendance_records:
            name = record['full_name']
            if record['time_in']:
                status = f"In: {record['time_in']}"
                if record['time_out']:
                    status += f", Out: {record['time_out']}"
                if record['overtime_in']:
                    status += f", OT-In: {record['overtime_in']}"
                if record['overtime_out']:
                    status += f", OT-Out: {record['overtime_out']}"
            else:
                status = "Not checked in"
            
            print(f"  {name}: {status}")

def identify_common_issues():
    """Identify common issues and provide solutions"""
    print("\n🔧 COMMON ISSUES & SOLUTIONS")
    print("=" * 50)
    
    issues_found = []
    
    # Check 1: CSRF Token issues
    print("1. Checking CSRF token handling...")
    with open('static/js/staff_dashboard.js', 'r') as f:
        js_content = f.read()
    
    if 'csrf_token' in js_content and 'getCSRFToken' in js_content:
        print("   ✅ CSRF token handling present")
    else:
        print("   ❌ CSRF token handling missing")
        issues_found.append("CSRF token handling")
    
    # Check 2: Biometric device connectivity
    print("2. Checking biometric device integration...")
    try:
        from zk_biometric import ZKBiometricDevice
        device = ZKBiometricDevice('*************')
        if device.connect():
            print("   ✅ Biometric device accessible")
            device.disconnect()
        else:
            print("   ❌ Cannot connect to biometric device")
            issues_found.append("Biometric device connectivity")
    except Exception as e:
        print(f"   ❌ Biometric device error: {e}")
        issues_found.append("Biometric device integration")
    
    # Check 3: Time validation logic
    print("3. Checking time validation logic...")
    current_time = datetime.datetime.now().time()
    if current_time < datetime.time(17, 0):
        print(f"   ⚠️ Current time ({current_time}) is before 5 PM - overtime will be blocked")
    else:
        print(f"   ✅ Current time ({current_time}) allows overtime")
    
    return issues_found

def main():
    """Main diagnostic function"""
    print("🔍 COMPREHENSIVE ATTENDANCE SYSTEM DIAGNOSTICS")
    print("=" * 60)
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_checks_passed = True
    
    # Run all diagnostic checks
    all_checks_passed &= check_database_schema()
    all_checks_passed &= check_biometric_verifications_table()
    all_checks_passed &= check_staff_dashboard_access()
    all_checks_passed &= check_verification_route()
    all_checks_passed &= check_javascript_functionality()
    
    check_current_attendance_status()
    issues = identify_common_issues()
    
    print("\n" + "=" * 60)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if all_checks_passed and not issues:
        print("🎉 ALL SYSTEMS OPERATIONAL!")
        print("✅ Database schema correct")
        print("✅ Routes working")
        print("✅ JavaScript functional")
        print("✅ Biometric integration working")
    else:
        print("⚠️ ISSUES IDENTIFIED:")
        if issues:
            for issue in issues:
                print(f"  - {issue}")
        
        print("\n🔧 RECOMMENDED ACTIONS:")
        print("1. Check browser console for JavaScript errors")
        print("2. Verify CSRF token is being sent with requests")
        print("3. Test biometric device connectivity")
        print("4. Check staff login credentials")
        print("5. Verify time-based restrictions")
    
    print(f"\n📞 For specific issues, please describe:")
    print("  - What error messages you see")
    print("  - Which verification type fails")
    print("  - Browser console errors")
    print("  - Time of day when testing")

if __name__ == '__main__':
    main()
