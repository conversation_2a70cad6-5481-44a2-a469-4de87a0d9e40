#!/usr/bin/env python3
"""
Debug Attendance Problems - Identify specific issues with check-in, check-out, overtime-in, overtime-out
"""

import requests
import json
import time
from app import app
from database import get_db
import datetime

def test_each_verification_type():
    """Test each verification type individually to identify problems"""
    print("🔍 DEBUGGING ATTENDANCE VERIFICATION PROBLEMS")
    print("=" * 70)
    
    with app.app_context():
        db = get_db()
        
        # Get a test staff member
        staff = db.execute('SELECT * FROM staff WHERE school_id = 4 LIMIT 1').fetchone()
        if not staff:
            print("❌ No staff found for testing")
            return
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing with staff: {staff_name} (ID: {staff_id})")
        
        # Clear any existing attendance for today
        today = datetime.date.today()
        db.execute('DELETE FROM attendance WHERE staff_id = ? AND date = ?', (staff_id, today))
        db.execute('DELETE FROM biometric_verifications WHERE staff_id = ? AND DATE(verification_time) = ?', (staff_id, today))
        db.commit()
        print(f"✅ Cleared existing attendance for {today}")
        
        # Test each verification type
        problems = []
        
        # Test 1: Check-in
        print("\n1️⃣ TESTING CHECK-IN")
        result = simulate_verification(staff_id, 'check-in')
        if result['success']:
            print("✅ Check-in working correctly")
        else:
            print(f"❌ Check-in problem: {result['error']}")
            problems.append(f"Check-in: {result['error']}")
        
        # Test 2: Check-out (should work after check-in)
        print("\n2️⃣ TESTING CHECK-OUT")
        result = simulate_verification(staff_id, 'check-out')
        if result['success']:
            print("✅ Check-out working correctly")
        else:
            print(f"❌ Check-out problem: {result['error']}")
            problems.append(f"Check-out: {result['error']}")
        
        # Test 3: Overtime-in (test time restriction)
        print("\n3️⃣ TESTING OVERTIME-IN")
        current_time = datetime.datetime.now().time()
        if current_time < datetime.time(17, 0):
            print(f"   Current time: {current_time} (before 5 PM)")
            result = simulate_verification(staff_id, 'overtime-in')
            if not result['success'] and 'after 17:00' in result['error']:
                print("✅ Overtime-in time restriction working correctly")
            else:
                print(f"❌ Overtime-in time restriction problem: {result}")
                problems.append(f"Overtime-in time restriction: {result}")
            
            # Test with simulated after-hours time
            print("   Testing with simulated after-hours time...")
            result = simulate_verification_with_time(staff_id, 'overtime-in', '18:00:00')
            if result['success']:
                print("✅ Overtime-in working correctly after hours")
            else:
                print(f"❌ Overtime-in after hours problem: {result['error']}")
                problems.append(f"Overtime-in after hours: {result['error']}")
        else:
            print(f"   Current time: {current_time} (after 5 PM)")
            result = simulate_verification(staff_id, 'overtime-in')
            if result['success']:
                print("✅ Overtime-in working correctly")
            else:
                print(f"❌ Overtime-in problem: {result['error']}")
                problems.append(f"Overtime-in: {result['error']}")
        
        # Test 4: Overtime-out
        print("\n4️⃣ TESTING OVERTIME-OUT")
        result = simulate_verification(staff_id, 'overtime-out')
        if result['success']:
            print("✅ Overtime-out working correctly")
        else:
            print(f"❌ Overtime-out problem: {result['error']}")
            problems.append(f"Overtime-out: {result['error']}")
        
        # Check final attendance record
        print("\n📊 FINAL ATTENDANCE RECORD")
        final_attendance = db.execute('''
            SELECT * FROM attendance WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        if final_attendance:
            print("✅ Attendance record created:")
            print(f"   Check-in: {final_attendance['time_in']}")
            print(f"   Check-out: {final_attendance['time_out']}")
            print(f"   Overtime-in: {final_attendance['overtime_in']}")
            print(f"   Overtime-out: {final_attendance['overtime_out']}")
            print(f"   Status: {final_attendance['status']}")
        else:
            print("❌ No attendance record found")
            problems.append("No attendance record created")
        
        # Summary
        print("\n" + "=" * 70)
        print("🎯 PROBLEM SUMMARY")
        print("=" * 70)
        
        if problems:
            print("❌ PROBLEMS IDENTIFIED:")
            for i, problem in enumerate(problems, 1):
                print(f"   {i}. {problem}")
        else:
            print("🎉 ALL VERIFICATION TYPES WORKING CORRECTLY!")
        
        return problems

def simulate_verification(staff_id, verification_type):
    """Simulate a verification without actual biometric check"""
    with app.app_context():
        from app import validate_verification_rules
        
        db = get_db()
        today = datetime.date.today()
        current_datetime = datetime.datetime.now()
        current_time = current_datetime.time().strftime('%H:%M:%S')
        
        try:
            # Get existing attendance
            existing_attendance = db.execute('''
                SELECT * FROM attendance WHERE staff_id = ? AND date = ?
            ''', (staff_id, today)).fetchone()
            
            # Validate rules
            validation_error = validate_verification_rules(verification_type, existing_attendance, current_datetime.time())
            if validation_error:
                return {'success': False, 'error': validation_error}
            
            # Log verification
            db.execute('''
                INSERT INTO biometric_verifications
                (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                VALUES (?, ?, ?, ?, ?, ?, 'success')
            ''', (staff_id, 4, verification_type, current_datetime, '*************', 'fingerprint'))
            
            # Update attendance
            LATE_ARRIVAL_TIME = datetime.time(9, 0)
            
            if verification_type == 'check-in':
                status = 'late' if current_datetime.time() > LATE_ARRIVAL_TIME else 'present'
                if existing_attendance:
                    db.execute('''
                        UPDATE attendance SET time_in = ?, status = ?
                        WHERE staff_id = ? AND date = ?
                    ''', (current_time, status, staff_id, today))
                else:
                    db.execute('''
                        INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (staff_id, 4, today, current_time, status))
            
            elif verification_type == 'check-out':
                db.execute('''
                    UPDATE attendance SET time_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            elif verification_type == 'overtime-in':
                db.execute('''
                    UPDATE attendance SET overtime_in = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            elif verification_type == 'overtime-out':
                db.execute('''
                    UPDATE attendance SET overtime_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            db.commit()
            return {'success': True, 'message': f'{verification_type} recorded at {current_time}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def simulate_verification_with_time(staff_id, verification_type, forced_time):
    """Simulate verification with a specific time"""
    with app.app_context():
        db = get_db()
        today = datetime.date.today()
        current_datetime = datetime.datetime.now()
        
        try:
            # Get existing attendance
            existing_attendance = db.execute('''
                SELECT * FROM attendance WHERE staff_id = ? AND date = ?
            ''', (staff_id, today)).fetchone()
            
            # For overtime-in, bypass time validation
            if verification_type == 'overtime-in':
                if not existing_attendance or not existing_attendance['time_out']:
                    return {'success': False, 'error': 'Cannot start overtime without completing regular check-out first'}
                if existing_attendance['overtime_in']:
                    return {'success': False, 'error': 'Already started overtime today'}
            
            # Log verification
            db.execute('''
                INSERT INTO biometric_verifications
                (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                VALUES (?, ?, ?, ?, ?, ?, 'success')
            ''', (staff_id, 4, verification_type, current_datetime, '*************', 'fingerprint'))
            
            # Update attendance with forced time
            if verification_type == 'overtime-in':
                db.execute('''
                    UPDATE attendance SET overtime_in = ?
                    WHERE staff_id = ? AND date = ?
                ''', (forced_time, staff_id, today))
            
            elif verification_type == 'overtime-out':
                db.execute('''
                    UPDATE attendance SET overtime_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (forced_time, staff_id, today))
            
            db.commit()
            return {'success': True, 'message': f'{verification_type} recorded at {forced_time}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def test_web_interface():
    """Test the web interface for attendance verification"""
    print("\n🌐 TESTING WEB INTERFACE")
    print("=" * 50)
    
    try:
        # Test if Flask app is running
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Flask app is running")
        else:
            print(f"❌ Flask app returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Flask app: {e}")
        return False
    
    return True

def main():
    """Main debugging function"""
    print("🐛 ATTENDANCE SYSTEM PROBLEM DEBUGGER")
    print("=" * 70)
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test backend logic
    problems = test_each_verification_type()
    
    # Test web interface
    web_working = test_web_interface()
    
    print("\n" + "=" * 70)
    print("🎯 FINAL DIAGNOSIS")
    print("=" * 70)
    
    if not problems and web_working:
        print("🎉 NO PROBLEMS FOUND!")
        print("✅ All verification types working correctly")
        print("✅ Web interface accessible")
        print("\nIf you're still experiencing issues, please describe:")
        print("  - What specific error messages you see")
        print("  - Which verification type fails")
        print("  - What happens when you click the verify button")
    else:
        print("❌ PROBLEMS IDENTIFIED:")
        if problems:
            print("\n🔧 BACKEND ISSUES:")
            for problem in problems:
                print(f"  - {problem}")
        
        if not web_working:
            print("\n🌐 WEB INTERFACE ISSUES:")
            print("  - Flask app not accessible")
        
        print("\n💡 RECOMMENDED FIXES:")
        print("  1. Check Flask app is running: python app.py")
        print("  2. Check database connectivity")
        print("  3. Check biometric device connection")
        print("  4. Check browser console for JavaScript errors")

if __name__ == '__main__':
    main()
