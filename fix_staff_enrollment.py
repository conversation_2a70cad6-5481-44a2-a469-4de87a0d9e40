#!/usr/bin/env python3
"""
Fix Staff Enrollment - Update database with actually enrolled staff
"""

from app import app
from database import get_db
from zk_biometric import ZKBiometricDevice
from werkzeug.security import generate_password_hash

def sync_enrolled_staff():
    """Sync database with actually enrolled staff on ZK device"""
    print("🔄 SYNCING STAFF ENROLLMENT")
    print("=" * 50)
    
    try:
        # Connect to ZK device
        device = ZKBiometricDevice('192.168.1.201')
        if not device.connect():
            print("❌ Cannot connect to ZK device")
            return False
        
        # Get users from device
        users = device.get_users()
        print(f"✅ Found {len(users)} users on ZK device")
        
        device.disconnect()
        
        with app.app_context():
            db = get_db()
            
            # Get current staff in database
            current_staff = db.execute('SELECT staff_id FROM staff WHERE school_id = 4').fetchall()
            current_staff_ids = [staff['staff_id'] for staff in current_staff]
            
            print(f"Current staff in database: {current_staff_ids}")
            
            # Get enrolled user IDs from device
            enrolled_ids = [str(user.get('user_id', '')) for user in users if user.get('user_id')]
            print(f"Enrolled IDs on device: {enrolled_ids[:10]}{'...' if len(enrolled_ids) > 10 else ''}")
            
            # Find staff that are enrolled on device but not in our database
            missing_in_db = [uid for uid in enrolled_ids if uid not in current_staff_ids]
            
            if missing_in_db:
                print(f"\n📝 Adding {len(missing_in_db)} enrolled staff to database:")
                
                for i, user_id in enumerate(missing_in_db[:5]):  # Add first 5 for testing
                    # Find user details from device data
                    user_data = next((u for u in users if str(u.get('user_id', '')) == user_id), None)
                    if user_data:
                        name = user_data.get('name', f'Staff {user_id}')
                        
                        # Add to database
                        password_hash = generate_password_hash('password123')
                        
                        db.execute('''
                            INSERT INTO staff (school_id, staff_id, password, password_hash, full_name, department, position)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ''', (4, user_id, 'password123', password_hash, name, 'General', 'Staff'))
                        
                        print(f"   ✅ Added: {name} (ID: {user_id})")
                
                db.commit()
                print(f"\n✅ Added {min(5, len(missing_in_db))} staff members to database")
            else:
                print("✅ All enrolled staff are already in database")
            
            # Show final staff list
            final_staff = db.execute('''
                SELECT staff_id, full_name FROM staff WHERE school_id = 4 ORDER BY staff_id
            ''').fetchall()
            
            print(f"\n📋 Final staff list ({len(final_staff)} total):")
            for staff in final_staff:
                enrolled_status = "✅ Enrolled" if staff['staff_id'] in enrolled_ids else "❌ Not Enrolled"
                print(f"   {staff['staff_id']}: {staff['full_name']} - {enrolled_status}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error syncing staff: {e}")
        return False

def test_with_enrolled_staff():
    """Test verification with actually enrolled staff"""
    print("\n🧪 TESTING WITH ENROLLED STAFF")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Get staff that should be enrolled (888, 889 from previous test)
        enrolled_staff = db.execute('''
            SELECT * FROM staff WHERE staff_id IN ('888', '889') AND school_id = 4
        ''').fetchall()
        
        if not enrolled_staff:
            print("❌ No enrolled staff found in database")
            return False
        
        print(f"Testing with {len(enrolled_staff)} enrolled staff:")
        
        for staff in enrolled_staff:
            print(f"\n🔍 Testing {staff['full_name']} (Staff ID: {staff['staff_id']})")
            
            # Test login credentials
            print(f"   Login: School ID: 4, Username: {staff['staff_id']}, Password: password123")
            
            # Test biometric verification
            from zk_biometric import verify_staff_biometric
            result = verify_staff_biometric(staff['staff_id'], '192.168.1.201', 'fingerprint')
            
            if result['success']:
                print(f"   ✅ Biometric verification: {result['message']}")
            else:
                print(f"   ❌ Biometric verification failed: {result['message']}")
        
        return True

def main():
    """Main function"""
    print("🔧 STAFF ENROLLMENT FIXER")
    print("=" * 50)
    
    # Sync enrolled staff
    if sync_enrolled_staff():
        # Test with enrolled staff
        test_with_enrolled_staff()
        
        print("\n" + "=" * 50)
        print("✅ STAFF ENROLLMENT FIXED!")
        print("=" * 50)
        print("🎯 NEXT STEPS:")
        print("1. Use enrolled staff IDs (888, 889) for testing")
        print("2. Login credentials: School ID: 4, Username: 888 or 889, Password: password123")
        print("3. Test biometric verification with these enrolled staff")
        print("4. If needed, enroll more staff using admin dashboard")
    else:
        print("\n❌ Failed to sync staff enrollment")

if __name__ == '__main__':
    main()
