from app import app
from database import get_db

with app.app_context():
    db = get_db()
    admins = db.execute('SELECT * FROM admins WHERE school_id = 4').fetchall()
    print('Admins for school 4:')
    for admin in admins:
        print(f'  ID: {admin["id"]}, Username: {admin["username"]}, Name: {admin["full_name"]}')
    
    print()
    print('All admins:')
    all_admins = db.execute('SELECT * FROM admins').fetchall()
    for admin in all_admins:
        print(f'  School: {admin["school_id"]}, Username: {admin["username"]}, Name: {admin["full_name"]}')
