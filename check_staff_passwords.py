#!/usr/bin/env python3
"""
Check Staff Passwords - Debug password issues
"""

from app import app
from database import get_db
from werkzeug.security import check_password_hash, generate_password_hash

def check_staff_passwords():
    """Check staff password fields and fix if needed"""
    print("🔍 CHECKING STAFF PASSWORDS")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Get all staff
        staff_list = db.execute('''
            SELECT id, staff_id, full_name, password, password_hash 
            FROM staff 
            WHERE school_id = 4
            ORDER BY staff_id
        ''').fetchall()
        
        print(f"Found {len(staff_list)} staff members:")
        
        for staff in staff_list:
            print(f"\n👤 {staff['full_name']} (Staff ID: {staff['staff_id']})")
            print(f"   Database ID: {staff['id']}")
            print(f"   Password field: {staff['password']}")
            print(f"   Password hash field: {staff['password_hash'][:50] if staff['password_hash'] else 'None'}...")
            
            # Test password verification
            if staff['password_hash']:
                test_password = 'password123'
                is_valid = check_password_hash(staff['password_hash'], test_password)
                print(f"   Password '{test_password}' valid: {is_valid}")
                
                if not is_valid:
                    print("   🔧 Fixing password hash...")
                    new_hash = generate_password_hash(test_password)
                    db.execute('''
                        UPDATE staff SET password_hash = ? WHERE id = ?
                    ''', (new_hash, staff['id']))
                    print("   ✅ Password hash updated")
            else:
                print("   🔧 Creating password hash...")
                new_hash = generate_password_hash('password123')
                db.execute('''
                    UPDATE staff SET password_hash = ? WHERE id = ?
                ''', (new_hash, staff['id']))
                print("   ✅ Password hash created")
        
        db.commit()
        print("\n✅ All staff passwords checked and fixed")

def test_login_logic():
    """Test the login logic directly"""
    print("\n🧪 TESTING LOGIN LOGIC")
    print("=" * 50)
    
    with app.app_context():
        db = get_db()
        
        # Test with staff ID 888
        staff_id = '888'
        school_id = '4'
        password = 'password123'
        
        print(f"Testing login: School ID: {school_id}, Staff ID: {staff_id}, Password: {password}")
        
        # Simulate the login query
        staff = db.execute('''
            SELECT * FROM staff 
            WHERE school_id = ? AND staff_id = ?
        ''', (school_id, staff_id)).fetchone()
        
        if staff:
            print(f"✅ Staff found: {staff['full_name']}")
            print(f"   Database ID: {staff['id']}")
            print(f"   Password hash: {staff['password_hash'][:50] if staff['password_hash'] else 'None'}...")
            
            if staff['password_hash']:
                is_valid = check_password_hash(staff['password_hash'], password)
                print(f"   Password verification: {is_valid}")
                
                if is_valid:
                    print("🎉 LOGIN SHOULD WORK!")
                else:
                    print("❌ Password verification failed")
            else:
                print("❌ No password hash found")
        else:
            print("❌ Staff not found")

def main():
    """Main function"""
    print("🔧 STAFF PASSWORD DEBUGGER")
    print("=" * 50)
    
    check_staff_passwords()
    test_login_logic()
    
    print("\n" + "=" * 50)
    print("✅ PASSWORD DEBUG COMPLETE")
    print("=" * 50)
    print("🎯 LOGIN CREDENTIALS:")
    print("   School ID: 4")
    print("   Username: 888 or 889")
    print("   Password: password123")

if __name__ == '__main__':
    main()
