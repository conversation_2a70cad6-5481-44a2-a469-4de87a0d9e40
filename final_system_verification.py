#!/usr/bin/env python3
"""
Final comprehensive system verification
"""

import requests
import re
import sqlite3
import json
import time
from datetime import datetime, date
from werkzeug.security import generate_password_hash

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def setup_fresh_test():
    """Setup fresh test environment"""
    print("🔄 Setting up fresh test environment...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Clear today's data for <PERSON> <PERSON>
        today = date.today()
        cursor.execute("DELETE FROM attendance WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?", (today,))
        cursor.execute("DELETE FROM biometric_verifications WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND DATE(verification_time) = ?", (today,))
        
        # Ensure password is set
        password_hash = generate_password_hash('test123')
        cursor.execute("UPDATE staff SET password_hash = ? WHERE staff_id = '888'", (password_hash,))
        
        db.commit()
        print("✅ Fresh test environment ready")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up fresh test: {e}")
        return False
    finally:
        db.close()

def verify_system_functionality():
    """Verify all system functionality"""
    print("🔍 FINAL SYSTEM VERIFICATION")
    print("=" * 60)
    
    # Setup fresh environment
    if not setup_fresh_test():
        return False
    
    base_url = 'http://127.0.0.1:5000'
    
    # Test 1: Admin Login and Dashboard
    print("\n1️⃣ Testing Admin Login and Dashboard...")
    admin_session = requests.Session()
    csrf_token = get_csrf_token(admin_session, f'{base_url}/')
    
    login_data = {
        'school_id': '4',
        'username': 'admin',
        'password': 'admin',
        'csrf_token': csrf_token
    }
    
    response = admin_session.post(f'{base_url}/login', data=login_data)
    if response.status_code == 200 and response.json().get('redirect'):
        print("✅ Admin login successful")
        
        # Test admin dashboard
        dashboard_response = admin_session.get(f'{base_url}/admin/dashboard')
        if dashboard_response.status_code == 200:
            print("✅ Admin dashboard accessible")
        else:
            print("❌ Admin dashboard not accessible")
            return False
    else:
        print("❌ Admin login failed")
        return False
    
    # Test 2: Staff Login and Dashboard
    print("\n2️⃣ Testing Staff Login and Dashboard...")
    staff_session = requests.Session()
    csrf_token = get_csrf_token(staff_session, f'{base_url}/')
    
    login_data = {
        'school_id': '4',
        'username': '888',
        'password': 'test123',
        'csrf_token': csrf_token
    }
    
    response = staff_session.post(f'{base_url}/login', data=login_data)
    if response.status_code == 200 and response.json().get('redirect'):
        print("✅ Staff login successful")
        
        # Test staff dashboard
        dashboard_response = staff_session.get(f'{base_url}/staff/dashboard')
        if dashboard_response.status_code == 200:
            print("✅ Staff dashboard accessible")
        else:
            print("❌ Staff dashboard not accessible")
            return False
    else:
        print("❌ Staff login failed")
        return False
    
    # Test 3: Complete Biometric Verification Workflow
    print("\n3️⃣ Testing Complete Biometric Verification Workflow...")
    
    verification_sequence = [
        ('check-in', 'Check-in'),
        ('check-out', 'Check-out'),
        ('overtime-in', 'Overtime-in'),
        ('overtime-out', 'Overtime-out')
    ]
    
    for verification_type, display_name in verification_sequence:
        print(f"   Testing {display_name}...")
        
        csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
        verification_data = {
            'device_ip': '*************',
            'biometric_method': 'fingerprint',
            'verification_type': verification_type,
            'csrf_token': csrf_token
        }
        
        response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ {display_name} successful - Time: {result.get('time_recorded')}")
            else:
                print(f"   ❌ {display_name} failed: {result.get('error')}")
                return False
        else:
            print(f"   ❌ {display_name} request failed")
            return False
        
        time.sleep(1)  # Small delay between verifications
    
    # Test 4: Real-time Admin Dashboard Updates
    print("\n4️⃣ Testing Real-time Admin Dashboard Updates...")
    
    response = admin_session.get(f'{base_url}/get_realtime_attendance')
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ Real-time attendance endpoint working")
            
            # Find Mohan Raj's data
            mohan_raj_data = None
            for record in data.get('attendance_data', []):
                if record.get('staff_number') == '888':
                    mohan_raj_data = record
                    break
            
            if mohan_raj_data:
                print("✅ Mohan Raj found in real-time data:")
                print(f"   Check-in: {mohan_raj_data.get('time_in')}")
                print(f"   Check-out: {mohan_raj_data.get('time_out')}")
                print(f"   Overtime-in: {mohan_raj_data.get('overtime_in')}")
                print(f"   Overtime-out: {mohan_raj_data.get('overtime_out')}")
                print(f"   Status: {mohan_raj_data.get('status')}")
                
                # Verify all four times are present
                required_fields = ['time_in', 'time_out', 'overtime_in', 'overtime_out']
                all_present = all(mohan_raj_data.get(field) for field in required_fields)
                
                if all_present:
                    print("✅ All four timing fields are present in admin dashboard")
                else:
                    print("❌ Some timing fields are missing in admin dashboard")
                    return False
            else:
                print("❌ Mohan Raj not found in real-time data")
                return False
        else:
            print("❌ Real-time attendance endpoint failed")
            return False
    else:
        print("❌ Real-time attendance request failed")
        return False
    
    # Test 5: Staff Profile Edit/Delete Buttons
    print("\n5️⃣ Testing Staff Profile Edit/Delete Buttons...")
    
    # Get Mohan Raj's database ID
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    cursor.execute("SELECT id FROM staff WHERE staff_id = '888'")
    result = cursor.fetchone()
    db.close()
    
    if result:
        staff_db_id = result[0]
        profile_url = f'{base_url}/admin/staff/{staff_db_id}'
        
        response = admin_session.get(profile_url)
        if response.status_code == 200:
            print("✅ Staff profile page accessible")
            
            page_content = response.text
            
            # Check for required elements
            required_elements = [
                ('editStaffBtn', 'Edit button'),
                ('deleteStaffBtn', 'Delete button'),
                ('staff_profile.js', 'Staff profile JavaScript'),
                ('csrf_token', 'CSRF token')
            ]
            
            all_elements_present = True
            for element, description in required_elements:
                if element in page_content:
                    print(f"   ✅ {description} found")
                else:
                    print(f"   ❌ {description} missing")
                    all_elements_present = False
            
            if not all_elements_present:
                return False
            
            # Test update functionality
            csrf_token = get_csrf_token(admin_session, profile_url)
            if csrf_token:
                update_data = {
                    'staff_id': staff_db_id,
                    'full_name': 'Mohan Raj',
                    'email': '<EMAIL>',
                    'phone': '9876543210',
                    'department': 'IT Department',
                    'position': 'Software Developer',
                    'csrf_token': csrf_token
                }
                
                response = admin_session.post(f'{base_url}/update_staff', data=update_data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print("✅ Staff update functionality working")
                    else:
                        print(f"❌ Staff update failed: {result.get('error')}")
                        return False
                else:
                    print("❌ Staff update request failed")
                    return False
            else:
                print("❌ Failed to get CSRF token for update test")
                return False
        else:
            print("❌ Staff profile page not accessible")
            return False
    else:
        print("❌ Mohan Raj not found in database")
        return False
    
    # Test 6: Database Consistency Check
    print("\n6️⃣ Final Database Consistency Check...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    # Check attendance record
    today = date.today()
    cursor.execute("""
        SELECT date, time_in, time_out, overtime_in, overtime_out, status
        FROM attendance 
        WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') 
        AND date = ?
    """, (today,))
    
    attendance = cursor.fetchone()
    
    if attendance:
        print("✅ Complete attendance record in database:")
        print(f"   Date: {attendance[0]}")
        print(f"   Check-in: {attendance[1]}")
        print(f"   Check-out: {attendance[2]}")
        print(f"   Overtime-in: {attendance[3]}")
        print(f"   Overtime-out: {attendance[4]}")
        print(f"   Status: {attendance[5]}")
        
        # Verify all times are recorded
        if all([attendance[1], attendance[2], attendance[3], attendance[4]]):
            print("✅ All four timing fields recorded in database")
        else:
            print("❌ Some timing fields missing in database")
            db.close()
            return False
    else:
        print("❌ No attendance record found in database")
        db.close()
        return False
    
    # Check biometric verifications
    cursor.execute("""
        SELECT COUNT(*) FROM biometric_verifications 
        WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') 
        AND DATE(verification_time) = ?
    """, (today,))
    
    verification_count = cursor.fetchone()[0]
    
    if verification_count == 4:
        print("✅ All four biometric verifications recorded")
    else:
        print(f"❌ Expected 4 verifications, found {verification_count}")
        db.close()
        return False
    
    db.close()
    
    print("\n🎉 ALL SYSTEM VERIFICATION TESTS PASSED!")
    print("=" * 60)
    print("✅ Admin login and dashboard working perfectly")
    print("✅ Staff login and dashboard working perfectly")
    print("✅ Complete biometric verification workflow working")
    print("✅ Real-time admin dashboard updates working")
    print("✅ Staff profile edit/delete buttons working")
    print("✅ Database consistency maintained")
    print("✅ All four timing options (check-in, check-out, overtime-in, overtime-out) working")
    print("✅ Admin dashboard shows real-time updates immediately")
    print("\n💡 THE SYSTEM IS FULLY FUNCTIONAL AND READY FOR USE!")
    
    return True

if __name__ == "__main__":
    verify_system_functionality()
