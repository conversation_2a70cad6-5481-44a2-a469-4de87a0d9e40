from app import app
from database import get_db

with app.app_context():
    db = get_db()
    columns = db.execute('PRAGMA table_info(schools)').fetchall()
    print('schools table columns:')
    for col in columns:
        print(f'  - {col["name"]} ({col["type"]})')
    
    print()
    print('Sample schools data:')
    schools = db.execute('SELECT * FROM schools LIMIT 3').fetchall()
    for school in schools:
        print(f'  ID: {school["id"]}, Name: {school["name"]}')
        for key in school.keys():
            print(f'    {key}: {school[key]}')
