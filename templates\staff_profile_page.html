<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Profile - VishnoRex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">VishnoRex - Admin</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">Dashboard</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <input type="text" class="form-control me-2" id="staffSearch" placeholder="Search staff..." style="width: 200px;">
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> {{ session.full_name }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#">Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Hidden CSRF token for JavaScript -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        {% include 'staff_profile.html' %}
    </div>

    <!-- Edit Staff Modal -->
    {% include 'edit_staff_modal.html' %}

    <!-- Biometric Enrollment Modal -->
    {% include 'biometric_modal.html' %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
    <script src="../static/js/staff_profile.js"></script>
    <script>
        // Load staff profile when page loads if ID is in URL
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const staffId = urlParams.get('id');
            if (staffId) {
                loadStaffProfile(staffId);
            }

            // Staff search functionality
            document.getElementById('staffSearch')?.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                window.location.href = `/admin/search_staff?q=${encodeURIComponent(searchTerm)}`;
            });
        });
    </script>
</body>
</html>