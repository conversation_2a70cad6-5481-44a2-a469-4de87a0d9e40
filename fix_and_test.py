#!/usr/bin/env python3
"""
Fix the biometric verification issue and test it
"""

import sqlite3
import datetime
from werkzeug.security import generate_password_hash

def fix_staff_password_and_test():
    """Fix staff password and create a simple test record"""
    print("🔧 Fixing staff login and creating test biometric verification...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # 1. Set password for <PERSON> (staff_id: 888)
        password_hash = generate_password_hash('test123')
        cursor.execute("""
            UPDATE staff SET password_hash = ? WHERE staff_id = '888'
        """, (password_hash,))
        print("✅ Updated staff password")
        
        # 2. Create a test attendance record directly
        today = datetime.date.today()
        current_time = datetime.datetime.now().time().strftime('%H:%M:%S')
        
        # Check if attendance record exists for today
        cursor.execute("""
            SELECT id FROM attendance WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?
        """, (today,))
        existing = cursor.fetchone()
        
        if existing:
            # Update existing record with check-in time
            cursor.execute("""
                UPDATE attendance 
                SET time_in = ?, status = 'present'
                WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?
            """, (current_time, today))
            print(f"✅ Updated existing attendance record with check-in time: {current_time}")
        else:
            # Create new attendance record
            cursor.execute("""
                INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                VALUES ((SELECT id FROM staff WHERE staff_id = '888'), 4, ?, ?, 'present')
            """, (today, current_time))
            print(f"✅ Created new attendance record with check-in time: {current_time}")
        
        # 3. Create biometric verification log
        cursor.execute("""
            INSERT INTO biometric_verifications 
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES ((SELECT id FROM staff WHERE staff_id = '888'), 4, 'check-in', ?, '*************', 'fingerprint', 'success')
        """, (datetime.datetime.now(),))
        print("✅ Created biometric verification log")
        
        db.commit()
        
        # 4. Verify the records were created
        cursor.execute("""
            SELECT s.full_name, a.date, a.time_in, a.time_out, a.overtime_in, a.overtime_out, a.status
            FROM attendance a
            JOIN staff s ON a.staff_id = s.id
            WHERE s.staff_id = '888' AND a.date = ?
        """, (today,))
        
        attendance_record = cursor.fetchone()
        if attendance_record:
            print(f"✅ Attendance record verified:")
            print(f"   Staff: {attendance_record[0]}")
            print(f"   Date: {attendance_record[1]}")
            print(f"   Check-in: {attendance_record[2]}")
            print(f"   Check-out: {attendance_record[3] or '--:--:--'}")
            print(f"   Overtime-in: {attendance_record[4] or '--:--:--'}")
            print(f"   Overtime-out: {attendance_record[5] or '--:--:--'}")
            print(f"   Status: {attendance_record[6]}")
            return True
        else:
            print("❌ Failed to verify attendance record")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def create_more_test_records():
    """Create additional test records for different verification types"""
    print("\n🔄 Creating additional test records...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        today = datetime.date.today()
        
        # Simulate check-out after 30 seconds
        import time
        time.sleep(2)  # Small delay
        checkout_time = datetime.datetime.now().time().strftime('%H:%M:%S')
        
        cursor.execute("""
            UPDATE attendance 
            SET time_out = ?
            WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?
        """, (checkout_time, today))
        
        # Log check-out verification
        cursor.execute("""
            INSERT INTO biometric_verifications 
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES ((SELECT id FROM staff WHERE staff_id = '888'), 4, 'check-out', ?, '*************', 'fingerprint', 'success')
        """, (datetime.datetime.now(),))
        
        print(f"✅ Added check-out time: {checkout_time}")
        
        # Simulate overtime-in
        time.sleep(2)
        overtime_in_time = datetime.datetime.now().time().strftime('%H:%M:%S')
        
        cursor.execute("""
            UPDATE attendance 
            SET overtime_in = ?
            WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?
        """, (overtime_in_time, today))
        
        # Log overtime-in verification
        cursor.execute("""
            INSERT INTO biometric_verifications 
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES ((SELECT id FROM staff WHERE staff_id = '888'), 4, 'overtime-in', ?, '*************', 'fingerprint', 'success')
        """, (datetime.datetime.now(),))
        
        print(f"✅ Added overtime-in time: {overtime_in_time}")
        
        # Simulate overtime-out
        time.sleep(2)
        overtime_out_time = datetime.datetime.now().time().strftime('%H:%M:%S')
        
        cursor.execute("""
            UPDATE attendance 
            SET overtime_out = ?
            WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?
        """, (overtime_out_time, today))
        
        # Log overtime-out verification
        cursor.execute("""
            INSERT INTO biometric_verifications 
            (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
            VALUES ((SELECT id FROM staff WHERE staff_id = '888'), 4, 'overtime-out', ?, '*************', 'fingerprint', 'success')
        """, (datetime.datetime.now(),))
        
        print(f"✅ Added overtime-out time: {overtime_out_time}")
        
        db.commit()
        
        # Show final record
        cursor.execute("""
            SELECT s.full_name, a.date, a.time_in, a.time_out, a.overtime_in, a.overtime_out, a.status
            FROM attendance a
            JOIN staff s ON a.staff_id = s.id
            WHERE s.staff_id = '888' AND a.date = ?
        """, (today,))
        
        final_record = cursor.fetchone()
        if final_record:
            print(f"\n🎉 COMPLETE ATTENDANCE RECORD:")
            print(f"   Staff: {final_record[0]}")
            print(f"   Date: {final_record[1]}")
            print(f"   Check-in: {final_record[2]}")
            print(f"   Check-out: {final_record[3]}")
            print(f"   Overtime-in: {final_record[4]}")
            print(f"   Overtime-out: {final_record[5]}")
            print(f"   Status: {final_record[6]}")
            return True
        
    except Exception as e:
        print(f"❌ Error creating additional records: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Biometric Verification Fix & Test")
    print("=" * 50)
    
    # Fix and create initial record
    success1 = fix_staff_password_and_test()
    
    if success1:
        # Create additional records
        success2 = create_more_test_records()
        
        if success2:
            print("\n" + "=" * 50)
            print("🎉 SUCCESS! All biometric verification types recorded!")
            print("\n💡 Next steps:")
            print("   1. Open admin dashboard: http://127.0.0.1:5000")
            print("   2. Login as admin")
            print("   3. Check today's attendance table")
            print("   4. You should see Mohan Raj's complete timing data!")
            print("   5. The real-time updates should show all four timing columns")
            print("\n📊 What you should see in admin dashboard:")
            print("   - Check-in time ✅")
            print("   - Check-out time ✅") 
            print("   - Overtime-in time ✅")
            print("   - Overtime-out time ✅")
            print("   - Status: Present ✅")
        else:
            print("\n❌ Failed to create additional records")
    else:
        print("\n❌ Failed to create initial record")
