from app import app
from database import get_db
import datetime

with app.app_context():
    db = get_db()
    today = datetime.date.today()
    
    # Check total attendance records
    total_records = db.execute('SELECT COUNT(*) as count FROM attendance').fetchone()
    print('Total attendance records:', total_records['count'])
    
    # Check today attendance
    today_records = db.execute('SELECT COUNT(*) as count FROM attendance WHERE date = ?', (today,)).fetchone()
    print('Today attendance records:', today_records['count'])
    
    # Check staff count
    staff_count = db.execute('SELECT COUNT(*) as count FROM staff WHERE school_id = 4').fetchone()
    print('Total staff in school 4:', staff_count['count'])
    
    # List all staff in school 4
    all_staff = db.execute('SELECT staff_id, full_name FROM staff WHERE school_id = 4').fetchall()
    print('Staff in school 4:')
    for staff in all_staff:
        print('  ID:', staff['staff_id'], '- Name:', staff['full_name'])
    
    # Check recent attendance
    recent = db.execute('SELECT * FROM attendance ORDER BY date DESC LIMIT 5').fetchall()
    print('Recent attendance records:')
    for record in recent:
        print('  Staff ID:', record['staff_id'], 'Date:', record['date'], 'Time In:', record['time_in'])
