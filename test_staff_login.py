#!/usr/bin/env python3
"""
Test staff login and biometric verification
"""

import requests
import re
import json

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        # Look for CSRF token in the HTML
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def test_staff_login_and_verification():
    """Test complete staff login and biometric verification flow"""
    print("🧪 Testing Staff Login and Biometric Verification")
    print("=" * 60)
    
    session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        # Step 1: Get login page and CSRF token
        print("1️⃣ Getting login page...")
        csrf_token = get_csrf_token(session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        print(f"✅ Got CSRF token: {csrf_token[:20]}...")
        
        # Step 2: Login as staff (we'll use one of the existing staff members)
        print("\n2️⃣ Attempting staff login...")
        
        # Try to login with test credentials
        login_data = {
            'username': 'TEST001',  # Staff ID from our debug
            'password': 'test123',
            'csrf_token': csrf_token
        }
        
        login_response = session.post(f'{base_url}/login', data=login_data)
        print(f"📡 Login response status: {login_response.status_code}")
        
        # Check if redirected to staff dashboard
        if 'staff/dashboard' in login_response.url or login_response.status_code == 302:
            print("✅ Staff login successful!")
        else:
            print("❌ Staff login failed")
            print(f"Response URL: {login_response.url}")
            print(f"Response text preview: {login_response.text[:200]}...")
            
            # Let's try creating a staff login first
            print("\n🔧 Creating staff login credentials...")
            return create_staff_login_and_test()
        
        # Step 3: Get staff dashboard to get new CSRF token
        print("\n3️⃣ Getting staff dashboard...")
        dashboard_response = session.get(f'{base_url}/staff/dashboard')
        if dashboard_response.status_code == 200:
            print("✅ Accessed staff dashboard")
            
            # Get new CSRF token from dashboard
            csrf_token = get_csrf_token(session, f'{base_url}/staff/dashboard')
            if csrf_token:
                print(f"✅ Got dashboard CSRF token: {csrf_token[:20]}...")
            else:
                print("❌ Failed to get dashboard CSRF token")
                return False
        else:
            print(f"❌ Failed to access staff dashboard: {dashboard_response.status_code}")
            return False
        
        # Step 4: Test biometric verification
        print("\n4️⃣ Testing biometric verification...")
        
        verification_data = {
            'verification_type': 'check-in',
            'biometric_method': 'fingerprint',
            'csrf_token': csrf_token
        }
        
        verification_response = session.post(f'{base_url}/biometric_attendance', data=verification_data)
        print(f"📡 Verification response status: {verification_response.status_code}")
        
        if verification_response.status_code == 200:
            try:
                result = verification_response.json()
                print(f"✅ Verification response: {result}")
                
                if result.get('success'):
                    print("🎉 Biometric verification successful!")
                    return True
                else:
                    print(f"❌ Verification failed: {result.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {verification_response.text[:200]}...")
                return False
        else:
            print(f"❌ Verification request failed: {verification_response.status_code}")
            print(f"Response: {verification_response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def create_staff_login_and_test():
    """Create staff login credentials and test"""
    print("🔧 Creating staff login credentials...")
    
    import sqlite3
    from werkzeug.security import generate_password_hash
    
    try:
        # Update staff password in database
        db = sqlite3.connect('vishnorex.db')
        cursor = db.cursor()
        
        # Check if we have the password_hash column
        cursor.execute("PRAGMA table_info(staff)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'password_hash' not in columns:
            print("❌ password_hash column missing. Adding it...")
            cursor.execute("ALTER TABLE staff ADD COLUMN password_hash TEXT")
        
        # Set password for a test staff member
        password_hash = generate_password_hash('test123')
        cursor.execute("""
            UPDATE staff SET password_hash = ? WHERE staff_id = '888'
        """, (password_hash,))
        
        db.commit()
        db.close()
        
        print("✅ Updated staff login credentials")
        
        # Now test login with updated credentials
        session = requests.Session()
        base_url = 'http://127.0.0.1:5000'
        
        # Get CSRF token
        csrf_token = get_csrf_token(session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        
        # Try login with updated credentials
        login_data = {
            'username': '888',  # Mohan Raj's staff ID
            'password': 'test123',
            'csrf_token': csrf_token
        }
        
        login_response = session.post(f'{base_url}/login', data=login_data)
        print(f"📡 Login attempt status: {login_response.status_code}")
        print(f"📡 Login response URL: {login_response.url}")
        
        if 'staff/dashboard' in login_response.url:
            print("✅ Staff login successful with updated credentials!")
            
            # Now test biometric verification
            csrf_token = get_csrf_token(session, f'{base_url}/staff/dashboard')
            if csrf_token:
                verification_data = {
                    'verification_type': 'check-in',
                    'biometric_method': 'fingerprint',
                    'csrf_token': csrf_token
                }
                
                verification_response = session.post(f'{base_url}/biometric_attendance', data=verification_data)
                if verification_response.status_code == 200:
                    result = verification_response.json()
                    print(f"🎉 Verification result: {result}")
                    return result.get('success', False)
                else:
                    print(f"❌ Verification failed: {verification_response.status_code}")
                    return False
            else:
                print("❌ Failed to get CSRF token from dashboard")
                return False
        else:
            print("❌ Login still failed")
            return False
            
    except Exception as e:
        print(f"❌ Error creating login: {e}")
        return False

if __name__ == "__main__":
    success = test_staff_login_and_verification()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS! Biometric verification is working!")
        print("💡 Staff members can now:")
        print("   1. Login to staff dashboard")
        print("   2. Select verification type")
        print("   3. Perform biometric verification")
        print("   4. See results in admin dashboard")
    else:
        print("❌ FAILED! Please check the issues above.")
        print("💡 Common solutions:")
        print("   1. Make sure staff have login credentials")
        print("   2. Check staff are logging into correct dashboard")
        print("   3. Verify CSRF tokens are working")
        print("   4. Check browser console for JavaScript errors")
