#!/usr/bin/env python3
"""
Test Check-in, Check-out, Overtime-in, Overtime-out functionality
"""

import requests
import json
import time
from app import app
from database import get_db
import datetime

def test_attendance_workflow():
    """Test the complete attendance workflow"""
    print("🔍 TESTING ATTENDANCE VERIFICATION WORKFLOW")
    print("=" * 60)
    
    with app.app_context():
        db = get_db()
        
        # Get a test staff member
        staff = db.execute('SELECT * FROM staff WHERE school_id = 4 LIMIT 1').fetchone()
        if not staff:
            print("❌ No staff found for testing")
            return
        
        staff_id = staff['id']
        staff_name = staff['full_name']
        print(f"Testing with staff: {staff_name} (ID: {staff_id})")
        
        # Clear any existing attendance for today
        today = datetime.date.today()
        db.execute('DELETE FROM attendance WHERE staff_id = ? AND date = ?', (staff_id, today))
        db.execute('DELETE FROM biometric_verifications WHERE staff_id = ? AND DATE(verification_time) = ?', (staff_id, today))
        db.commit()
        print(f"✅ Cleared existing attendance for {today}")
        
        # Test 1: Check-in
        print("\n📍 TEST 1: CHECK-IN")
        result = test_verification_type(staff_id, 'check-in')
        if result['success']:
            print("✅ Check-in successful")
        else:
            print(f"❌ Check-in failed: {result['error']}")
            return
        
        # Test 2: Try duplicate check-in (should fail)
        print("\n📍 TEST 2: DUPLICATE CHECK-IN (should fail)")
        result = test_verification_type(staff_id, 'check-in')
        if not result['success']:
            print(f"✅ Duplicate check-in correctly blocked: {result['error']}")
        else:
            print("❌ Duplicate check-in should have been blocked")
        
        # Test 3: Check-out
        print("\n📍 TEST 3: CHECK-OUT")
        result = test_verification_type(staff_id, 'check-out')
        if result['success']:
            print("✅ Check-out successful")
        else:
            print(f"❌ Check-out failed: {result['error']}")
            return
        
        # Test 4: Try overtime-in before 5 PM (should fail if before 5 PM)
        current_time = datetime.datetime.now().time()
        print(f"\n📍 TEST 4: OVERTIME-IN (current time: {current_time})")
        result = test_verification_type(staff_id, 'overtime-in')
        if current_time < datetime.time(17, 0):
            if not result['success']:
                print(f"✅ Overtime-in correctly blocked before 5 PM: {result['error']}")
            else:
                print("❌ Overtime-in should be blocked before 5 PM")
        else:
            if result['success']:
                print("✅ Overtime-in successful after 5 PM")
            else:
                print(f"❌ Overtime-in failed: {result['error']}")
        
        # Test 5: Force overtime-in by updating time (simulate after 5 PM)
        print("\n📍 TEST 5: FORCE OVERTIME-IN (simulating after 5 PM)")
        # Temporarily modify the validation to allow overtime
        result = test_verification_type_with_time(staff_id, 'overtime-in', '17:30:00')
        if result['success']:
            print("✅ Overtime-in successful")
        else:
            print(f"❌ Overtime-in failed: {result['error']}")
        
        # Test 6: Overtime-out
        print("\n📍 TEST 6: OVERTIME-OUT")
        result = test_verification_type_with_time(staff_id, 'overtime-out', '19:00:00')
        if result['success']:
            print("✅ Overtime-out successful")
        else:
            print(f"❌ Overtime-out failed: {result['error']}")
        
        # Test 7: Check final attendance record
        print("\n📍 TEST 7: FINAL ATTENDANCE RECORD")
        final_attendance = db.execute('''
            SELECT * FROM attendance WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        if final_attendance:
            print("✅ Final attendance record:")
            print(f"   Check-in: {final_attendance['time_in']}")
            print(f"   Check-out: {final_attendance['time_out']}")
            print(f"   Overtime-in: {final_attendance['overtime_in']}")
            print(f"   Overtime-out: {final_attendance['overtime_out']}")
            print(f"   Status: {final_attendance['status']}")
        else:
            print("❌ No final attendance record found")

def test_verification_type(staff_id, verification_type):
    """Test a specific verification type"""
    with app.app_context():
        from app import validate_verification_rules
        
        db = get_db()
        today = datetime.date.today()
        current_datetime = datetime.datetime.now()
        current_time = current_datetime.time().strftime('%H:%M:%S')
        
        # Get existing attendance
        existing_attendance = db.execute('''
            SELECT * FROM attendance WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        # Validate rules
        validation_error = validate_verification_rules(verification_type, existing_attendance, current_datetime.time())
        if validation_error:
            return {'success': False, 'error': validation_error}
        
        # Simulate successful verification (skip actual biometric check)
        try:
            # Log verification
            db.execute('''
                INSERT INTO biometric_verifications
                (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                VALUES (?, ?, ?, ?, ?, ?, 'success')
            ''', (staff_id, 4, verification_type, current_datetime, '*************', 'fingerprint'))
            
            # Update attendance
            LATE_ARRIVAL_TIME = datetime.time(9, 0)
            
            if verification_type == 'check-in':
                status = 'late' if current_datetime.time() > LATE_ARRIVAL_TIME else 'present'
                if existing_attendance:
                    db.execute('''
                        UPDATE attendance SET time_in = ?, status = ?
                        WHERE staff_id = ? AND date = ?
                    ''', (current_time, status, staff_id, today))
                else:
                    db.execute('''
                        INSERT INTO attendance (staff_id, school_id, date, time_in, status)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (staff_id, 4, today, current_time, status))
            
            elif verification_type == 'check-out':
                db.execute('''
                    UPDATE attendance SET time_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            elif verification_type == 'overtime-in':
                db.execute('''
                    UPDATE attendance SET overtime_in = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            elif verification_type == 'overtime-out':
                db.execute('''
                    UPDATE attendance SET overtime_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (current_time, staff_id, today))
            
            db.commit()
            return {'success': True, 'message': f'{verification_type} recorded at {current_time}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def test_verification_type_with_time(staff_id, verification_type, forced_time):
    """Test verification with a specific time"""
    with app.app_context():
        db = get_db()
        today = datetime.date.today()
        current_datetime = datetime.datetime.now()
        
        # Get existing attendance
        existing_attendance = db.execute('''
            SELECT * FROM attendance WHERE staff_id = ? AND date = ?
        ''', (staff_id, today)).fetchone()
        
        # For overtime-in, we need to bypass time validation
        if verification_type == 'overtime-in':
            if not existing_attendance or not existing_attendance['time_out']:
                return {'success': False, 'error': 'Cannot start overtime without completing regular check-out first'}
            if existing_attendance['overtime_in']:
                return {'success': False, 'error': 'Already started overtime today'}
        
        elif verification_type == 'overtime-out':
            if not existing_attendance or not existing_attendance['overtime_in']:
                return {'success': False, 'error': 'Cannot end overtime without starting overtime first'}
            if existing_attendance['overtime_out']:
                return {'success': False, 'error': 'Already ended overtime today'}
        
        try:
            # Log verification
            db.execute('''
                INSERT INTO biometric_verifications
                (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                VALUES (?, ?, ?, ?, ?, ?, 'success')
            ''', (staff_id, 4, verification_type, current_datetime, '*************', 'fingerprint'))
            
            # Update attendance with forced time
            if verification_type == 'overtime-in':
                db.execute('''
                    UPDATE attendance SET overtime_in = ?
                    WHERE staff_id = ? AND date = ?
                ''', (forced_time, staff_id, today))
            
            elif verification_type == 'overtime-out':
                db.execute('''
                    UPDATE attendance SET overtime_out = ?
                    WHERE staff_id = ? AND date = ?
                ''', (forced_time, staff_id, today))
            
            db.commit()
            return {'success': True, 'message': f'{verification_type} recorded at {forced_time}'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

def main():
    """Main test function"""
    print("🧪 ATTENDANCE VERIFICATION SYSTEM TEST")
    print("=" * 60)
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_attendance_workflow()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print("✅ Attendance verification workflow tested")
    print("✅ Business rules validation tested")
    print("✅ Database operations tested")
    print("\n🎯 If any tests failed, the issues have been identified!")

if __name__ == '__main__':
    main()
