#!/usr/bin/env python3
"""
Diagnose why biometric check-in from device doesn't show in admin dashboard
"""

import requests
import re
import sqlite3
import json
from datetime import datetime, date
from werkzeug.security import generate_password_hash

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def check_database_state():
    """Check current database state"""
    print("🗃️  Checking Database State...")
    
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        today = date.today()
        
        # Check staff records
        cursor.execute("SELECT id, staff_id, full_name, school_id FROM staff WHERE staff_id = '888'")
        staff = cursor.fetchone()
        
        if staff:
            staff_db_id, staff_id, full_name, school_id = staff
            print(f"✅ Staff found: ID={staff_db_id}, Staff ID={staff_id}, Name={full_name}, School={school_id}")
            
            # Check today's attendance
            cursor.execute("""
                SELECT date, time_in, time_out, overtime_in, overtime_out, status
                FROM attendance 
                WHERE staff_id = ? AND date = ?
            """, (staff_db_id, today))
            
            attendance = cursor.fetchone()
            if attendance:
                print("✅ Today's attendance record found:")
                print(f"   Date: {attendance[0]}")
                print(f"   Check-in: {attendance[1] or 'Not recorded'}")
                print(f"   Check-out: {attendance[2] or 'Not recorded'}")
                print(f"   Overtime-in: {attendance[3] or 'Not recorded'}")
                print(f"   Overtime-out: {attendance[4] or 'Not recorded'}")
                print(f"   Status: {attendance[5] or 'Unknown'}")
            else:
                print("❌ No attendance record found for today")
            
            # Check biometric verifications
            cursor.execute("""
                SELECT verification_type, verification_time, verification_status, device_ip, biometric_method
                FROM biometric_verifications 
                WHERE staff_id = ? AND DATE(verification_time) = ?
                ORDER BY verification_time DESC
            """, (staff_db_id, today))
            
            verifications = cursor.fetchall()
            if verifications:
                print(f"✅ {len(verifications)} biometric verification(s) found today:")
                for i, (v_type, v_time, v_status, device_ip, method) in enumerate(verifications, 1):
                    print(f"   {i}. {v_type} at {v_time} - {v_status} (Device: {device_ip}, Method: {method})")
            else:
                print("❌ No biometric verifications found for today")
            
            return staff_db_id, school_id
        else:
            print("❌ Staff with ID '888' not found")
            return None, None
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return None, None
    finally:
        db.close()

def test_admin_dashboard_data(school_id):
    """Test admin dashboard data retrieval"""
    print("\n📊 Testing Admin Dashboard Data Retrieval...")
    
    # Login as admin
    admin_session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        csrf_token = get_csrf_token(admin_session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        
        login_data = {
            'school_id': str(school_id),
            'username': 'admin',
            'password': 'admin',
            'csrf_token': csrf_token
        }
        
        response = admin_session.post(f'{base_url}/login', data=login_data)
        if response.status_code == 200 and response.json().get('redirect'):
            print("✅ Admin login successful")
            
            # Test real-time attendance endpoint
            response = admin_session.get(f'{base_url}/get_realtime_attendance')
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ Real-time attendance endpoint working")
                    
                    # Find staff with ID 888
                    found_staff = None
                    for record in data.get('attendance_data', []):
                        if record.get('staff_number') == '888':
                            found_staff = record
                            break
                    
                    if found_staff:
                        print("✅ Staff '888' found in admin dashboard:")
                        print(f"   Name: {found_staff.get('full_name')}")
                        print(f"   Check-in: {found_staff.get('time_in') or 'Not recorded'}")
                        print(f"   Check-out: {found_staff.get('time_out') or 'Not recorded'}")
                        print(f"   Overtime-in: {found_staff.get('overtime_in') or 'Not recorded'}")
                        print(f"   Overtime-out: {found_staff.get('overtime_out') or 'Not recorded'}")
                        print(f"   Status: {found_staff.get('status') or 'Unknown'}")
                        return True
                    else:
                        print("❌ Staff '888' NOT found in admin dashboard data")
                        print("📋 Available staff in dashboard:")
                        for i, record in enumerate(data.get('attendance_data', [])[:5], 1):
                            print(f"   {i}. {record.get('staff_number')} - {record.get('full_name')}")
                        return False
                else:
                    print(f"❌ Real-time attendance endpoint error: {data.get('error')}")
                    return False
            else:
                print(f"❌ Real-time attendance endpoint failed: {response.status_code}")
                return False
        else:
            print("❌ Admin login failed")
            return False
            
    except Exception as e:
        print(f"❌ Admin dashboard test error: {e}")
        return False

def test_staff_biometric_verification(staff_db_id):
    """Test staff biometric verification through web interface"""
    print("\n🔐 Testing Staff Biometric Verification (Web Interface)...")
    
    # Login as staff
    staff_session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        csrf_token = get_csrf_token(staff_session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        
        login_data = {
            'school_id': '4',
            'username': '888',
            'password': 'test123',
            'csrf_token': csrf_token
        }
        
        response = staff_session.post(f'{base_url}/login', data=login_data)
        if response.status_code == 200 and response.json().get('redirect'):
            print("✅ Staff login successful")
            
            # Clear existing attendance for clean test
            db = sqlite3.connect('vishnorex.db')
            cursor = db.cursor()
            today = date.today()
            cursor.execute("DELETE FROM attendance WHERE staff_id = ? AND date = ?", (staff_db_id, today))
            cursor.execute("DELETE FROM biometric_verifications WHERE staff_id = ? AND DATE(verification_time) = ?", (staff_db_id, today))
            db.commit()
            db.close()
            print("✅ Cleared existing attendance for clean test")
            
            # Perform biometric verification
            csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
            verification_data = {
                'device_ip': '*************',
                'biometric_method': 'fingerprint',
                'verification_type': 'check-in',
                'csrf_token': csrf_token
            }
            
            response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ Biometric verification successful - Time: {result.get('time_recorded')}")
                    return True
                else:
                    print(f"❌ Biometric verification failed: {result.get('error')}")
                    return False
            else:
                print(f"❌ Biometric verification request failed: {response.status_code}")
                return False
        else:
            print("❌ Staff login failed")
            return False
            
    except Exception as e:
        print(f"❌ Staff verification test error: {e}")
        return False

def test_zk_device_sync():
    """Test ZK device sync functionality"""
    print("\n🔄 Testing ZK Device Sync...")
    
    # Login as admin
    admin_session = requests.Session()
    base_url = 'http://127.0.0.1:5000'
    
    try:
        csrf_token = get_csrf_token(admin_session, f'{base_url}/')
        if not csrf_token:
            print("❌ Failed to get CSRF token")
            return False
        
        login_data = {
            'school_id': '4',
            'username': 'admin',
            'password': 'admin',
            'csrf_token': csrf_token
        }
        
        response = admin_session.post(f'{base_url}/login', data=login_data)
        if response.status_code == 200 and response.json().get('redirect'):
            print("✅ Admin login successful")
            
            # Test ZK device sync
            csrf_token = get_csrf_token(admin_session, f'{base_url}/admin/dashboard')
            sync_data = {
                'device_ip': '*************',
                'csrf_token': csrf_token
            }
            
            response = admin_session.post(f'{base_url}/sync_biometric_attendance', data=sync_data)
            if response.status_code == 200:
                result = response.json()
                print(f"📡 ZK Sync Result:")
                print(f"   Success: {result.get('success')}")
                print(f"   Message: {result.get('message', 'No message')}")
                print(f"   Total Records: {result.get('total_records', 0)}")
                print(f"   SQLite Synced: {result.get('sqlite_synced', 0)}")
                print(f"   MySQL Synced: {result.get('mysql_synced', 0)}")
                return result.get('success', False)
            else:
                print(f"❌ ZK sync request failed: {response.status_code}")
                return False
        else:
            print("❌ Admin login failed")
            return False
            
    except Exception as e:
        print(f"❌ ZK sync test error: {e}")
        return False

def main():
    """Run comprehensive diagnostic"""
    print("🔍 BIOMETRIC CHECK-IN DIAGNOSTIC")
    print("=" * 60)
    
    # Step 1: Check database state
    staff_db_id, school_id = check_database_state()
    if not staff_db_id:
        print("❌ Cannot proceed without valid staff record")
        return
    
    # Step 2: Test admin dashboard data retrieval
    dashboard_working = test_admin_dashboard_data(school_id)
    
    # Step 3: Test staff biometric verification through web
    web_verification_working = test_staff_biometric_verification(staff_db_id)
    
    # Step 4: Check database state after web verification
    if web_verification_working:
        print("\n🔄 Checking Database After Web Verification...")
        check_database_state()
        
        # Test admin dashboard again
        print("\n📊 Testing Admin Dashboard After Web Verification...")
        dashboard_after_web = test_admin_dashboard_data(school_id)
    
    # Step 5: Test ZK device sync
    zk_sync_working = test_zk_device_sync()
    
    # Step 6: Final diagnosis
    print("\n🎯 DIAGNOSTIC RESULTS")
    print("=" * 60)
    
    if dashboard_working and web_verification_working:
        print("✅ Web-based biometric verification works correctly")
        print("✅ Admin dashboard shows web-based verifications")
        
        if not zk_sync_working:
            print("❌ ZK device sync has issues")
            print("\n💡 SOLUTION:")
            print("1. Check ZK device connection (IP: *************)")
            print("2. Ensure device is accessible from the server")
            print("3. Verify staff is enrolled on the ZK device with ID '888'")
            print("4. After using physical device, click 'Sync Attendance' in admin dashboard")
        else:
            print("✅ ZK device sync works")
            print("\n💡 WORKFLOW:")
            print("1. Use biometric device for check-in")
            print("2. Go to admin dashboard")
            print("3. Click 'Sync Attendance' button to sync from device")
            print("4. Check-in should appear in real-time dashboard")
    else:
        print("❌ Basic biometric verification system has issues")
        print("\n💡 TROUBLESHOOTING NEEDED:")
        print("1. Check staff login credentials")
        print("2. Verify database connectivity")
        print("3. Check admin dashboard permissions")

if __name__ == "__main__":
    main()
