#!/usr/bin/env python3
"""
Detailed Biometric Verification Test
Tests the exact biometric verification process to identify issues
"""

import requests
import json
import time
from app import app
from database import get_db
from zk_biometric import verify_staff_biometric, ZKBiometricDevice
import datetime

def test_biometric_device_connection():
    """Test direct connection to biometric device"""
    print("🔌 TESTING BIOMETRIC DEVICE CONNECTION")
    print("=" * 60)
    
    try:
        device = ZKBiometricDevice('*************')
        if device.connect():
            print("✅ Successfully connected to ZK device")
            
            # Get users from device
            users = device.get_users()
            print(f"✅ Found {len(users)} users on device")
            
            # Show first few users for debugging
            print("\n📋 Sample users on device:")
            for i, user in enumerate(users[:5]):
                print(f"   {i+1}. User ID: {user.get('user_id', 'N/A')}, Name: {user.get('name', 'N/A')}")
            
            device.disconnect()
            return True, users
        else:
            print("❌ Failed to connect to ZK device")
            return False, []
    except Exception as e:
        print(f"❌ Error connecting to device: {e}")
        return False, []

def test_staff_biometric_verification():
    """Test biometric verification for actual staff"""
    print("\n👤 TESTING STAFF BIOMETRIC VERIFICATION")
    print("=" * 60)
    
    with app.app_context():
        db = get_db()
        
        # Get all staff with their IDs
        staff_list = db.execute('''
            SELECT id, staff_id, full_name, school_id 
            FROM staff 
            WHERE school_id = 4
            ORDER BY staff_id
        ''').fetchall()
        
        print(f"Found {len(staff_list)} staff members:")
        for staff in staff_list:
            print(f"   DB ID: {staff['id']}, Staff ID: {staff['staff_id']}, Name: {staff['full_name']}")
        
        # Test verification for each staff member
        verification_results = []
        
        for staff in staff_list:
            print(f"\n🔍 Testing verification for {staff['full_name']} (Staff ID: {staff['staff_id']})")
            
            try:
                result = verify_staff_biometric(staff['staff_id'], '*************', 'fingerprint')
                verification_results.append({
                    'staff_name': staff['full_name'],
                    'staff_id': staff['staff_id'],
                    'result': result
                })
                
                if result['success']:
                    print(f"   ✅ Verification successful: {result['message']}")
                else:
                    print(f"   ❌ Verification failed: {result['message']}")
                    
            except Exception as e:
                print(f"   ❌ Verification error: {e}")
                verification_results.append({
                    'staff_name': staff['full_name'],
                    'staff_id': staff['staff_id'],
                    'result': {'success': False, 'message': str(e)}
                })
        
        return verification_results

def test_web_interface_verification():
    """Test the web interface biometric verification"""
    print("\n🌐 TESTING WEB INTERFACE VERIFICATION")
    print("=" * 60)
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5000"
    
    try:
        # Get login page and extract CSRF token
        response = session.get(f"{base_url}/")
        if response.status_code != 200:
            print(f"❌ Cannot access login page: {response.status_code}")
            return False
        
        # Extract CSRF token
        import re
        csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', response.text)
        if not csrf_match:
            print("❌ Cannot extract CSRF token")
            return False
        
        csrf_token = csrf_match.group(1)
        print(f"✅ CSRF token extracted: {csrf_token[:20]}...")
        
        # Login as staff
        login_data = {
            'school_id': '4',
            'username': '5222',  # Staff ID
            'password': 'password123',
            'csrf_token': csrf_token
        }
        
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success') or result.get('redirect'):
                print("✅ Staff login successful")
                
                # Get dashboard and new CSRF token
                dashboard_response = session.get(f"{base_url}{result.get('redirect', '/staff/dashboard')}")
                csrf_match = re.search(r'name="csrf_token".*?value="([^"]+)"', dashboard_response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"✅ Dashboard CSRF token: {csrf_token[:20]}...")
                    
                    # Test each verification type
                    verification_types = ['check-in', 'check-out', 'overtime-in', 'overtime-out']
                    
                    for verification_type in verification_types:
                        print(f"\n🔍 Testing {verification_type} via web interface...")
                        
                        verification_data = {
                            'verification_type': verification_type,
                            'biometric_method': 'fingerprint',
                            'device_ip': '*************',
                            'csrf_token': csrf_token
                        }
                        
                        response = session.post(f"{base_url}/biometric_attendance", data=verification_data)
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                print(f"   ✅ {verification_type} successful: {result.get('message')}")
                            else:
                                error_msg = result.get('error', 'Unknown error')
                                if 'already' in error_msg.lower() or 'cannot' in error_msg.lower():
                                    print(f"   ⚠️ {verification_type} blocked (expected): {error_msg}")
                                else:
                                    print(f"   ❌ {verification_type} failed: {error_msg}")
                        else:
                            print(f"   ❌ {verification_type} request failed: {response.status_code}")
                            if response.text:
                                print(f"      Response: {response.text[:200]}...")
                    
                    return True
                else:
                    print("❌ Cannot get dashboard CSRF token")
                    return False
            else:
                print(f"❌ Login failed: {result}")
                return False
        else:
            print(f"❌ Login request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web interface test error: {e}")
        return False

def analyze_problems(device_connected, users, verification_results, web_test_passed):
    """Analyze all test results and identify specific problems"""
    print("\n" + "=" * 70)
    print("🔍 PROBLEM ANALYSIS")
    print("=" * 70)
    
    problems = []
    
    # Check device connection
    if not device_connected:
        problems.append("❌ Cannot connect to ZK biometric device (*************)")
    else:
        print("✅ ZK device connection working")
    
    # Check user enrollment
    if device_connected and users:
        enrolled_staff_ids = [str(user.get('user_id', '')) for user in users]
        print(f"✅ {len(users)} users enrolled on device")
        print(f"   Enrolled IDs: {', '.join(enrolled_staff_ids[:10])}{'...' if len(enrolled_staff_ids) > 10 else ''}")
        
        # Check if our test staff are enrolled
        test_staff_ids = ['5222', '888', '889', '7078', '584', '9999']
        missing_staff = [sid for sid in test_staff_ids if sid not in enrolled_staff_ids]
        if missing_staff:
            problems.append(f"❌ Test staff not enrolled on device: {', '.join(missing_staff)}")
        else:
            print("✅ Test staff are enrolled on device")
    
    # Check verification results
    if verification_results:
        successful_verifications = [r for r in verification_results if r['result']['success']]
        failed_verifications = [r for r in verification_results if not r['result']['success']]
        
        print(f"✅ {len(successful_verifications)} staff verifications successful")
        if failed_verifications:
            print(f"❌ {len(failed_verifications)} staff verifications failed:")
            for failed in failed_verifications:
                print(f"   - {failed['staff_name']} (ID: {failed['staff_id']}): {failed['result']['message']}")
                problems.append(f"❌ {failed['staff_name']} verification failed: {failed['result']['message']}")
    
    # Check web interface
    if not web_test_passed:
        problems.append("❌ Web interface verification not working")
    else:
        print("✅ Web interface verification working")
    
    return problems

def main():
    """Main test function"""
    print("🧪 DETAILED BIOMETRIC VERIFICATION TEST")
    print("=" * 70)
    print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Device connection
    device_connected, users = test_biometric_device_connection()
    
    # Test 2: Staff verification
    verification_results = test_staff_biometric_verification()
    
    # Test 3: Web interface
    web_test_passed = test_web_interface_verification()
    
    # Analyze results
    problems = analyze_problems(device_connected, users, verification_results, web_test_passed)
    
    print("\n" + "=" * 70)
    print("🎯 FINAL DIAGNOSIS")
    print("=" * 70)
    
    if not problems:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Biometric verification system is working correctly")
        print("\nIf you're still having issues, please check:")
        print("  - Browser console for JavaScript errors")
        print("  - Network connectivity to the device")
        print("  - Staff enrollment on the physical device")
    else:
        print("❌ PROBLEMS IDENTIFIED:")
        for i, problem in enumerate(problems, 1):
            print(f"   {i}. {problem}")
        
        print("\n💡 RECOMMENDED SOLUTIONS:")
        if any("device" in p.lower() for p in problems):
            print("  🔧 Device Issues:")
            print("    - Check device IP (*************) is accessible")
            print("    - Verify device is powered on and connected to network")
            print("    - Test device connection from admin dashboard")
        
        if any("enrolled" in p.lower() for p in problems):
            print("  👤 Enrollment Issues:")
            print("    - Use admin dashboard to enroll missing staff")
            print("    - Verify staff IDs match between database and device")
            print("    - Check biometric data was properly captured")
        
        if any("web interface" in p.lower() for p in problems):
            print("  🌐 Web Interface Issues:")
            print("    - Check Flask app is running")
            print("    - Verify CSRF tokens are working")
            print("    - Check browser console for JavaScript errors")

if __name__ == '__main__':
    main()
