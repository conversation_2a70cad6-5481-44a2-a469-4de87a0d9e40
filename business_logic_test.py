#!/usr/bin/env python3
"""
Test business logic validation for biometric attendance system
"""

import requests
import re
import sqlite3
import json
import time
from datetime import datetime, date
from werkzeug.security import generate_password_hash

def get_csrf_token(session, url):
    """Extract CSRF token from a page"""
    response = session.get(url)
    if response.status_code == 200:
        match = re.search(r'name="csrf_token" value="([^"]+)"', response.text)
        if match:
            return match.group(1)
    return None

def test_business_logic_validation():
    """Test that business logic validation is working correctly"""
    print("🔍 BUSINESS LOGIC VALIDATION TEST")
    print("=" * 60)
    
    # Setup fresh environment
    db = sqlite3.connect('vishnorex.db')
    cursor = db.cursor()
    
    try:
        # Clear today's data for Mohan Raj
        today = date.today()
        cursor.execute("DELETE FROM attendance WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND date = ?", (today,))
        cursor.execute("DELETE FROM biometric_verifications WHERE staff_id = (SELECT id FROM staff WHERE staff_id = '888') AND DATE(verification_time) = ?", (today,))
        
        # Ensure password is set
        password_hash = generate_password_hash('test123')
        cursor.execute("UPDATE staff SET password_hash = ? WHERE staff_id = '888'", (password_hash,))
        
        db.commit()
        print("✅ Fresh test environment ready")
        
    except Exception as e:
        print(f"❌ Error setting up test: {e}")
        return False
    finally:
        db.close()
    
    base_url = 'http://127.0.0.1:5000'
    
    # Login as staff
    staff_session = requests.Session()
    csrf_token = get_csrf_token(staff_session, f'{base_url}/')
    
    login_data = {
        'school_id': '4',
        'username': '888',
        'password': 'test123',
        'csrf_token': csrf_token
    }
    
    response = staff_session.post(f'{base_url}/login', data=login_data)
    if not (response.status_code == 200 and response.json().get('redirect')):
        print("❌ Staff login failed")
        return False
    
    print("✅ Staff login successful")
    
    # Test 1: Check-in (should work anytime)
    print("\n1️⃣ Testing Check-in (should work anytime)...")
    csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
    verification_data = {
        'device_ip': '*************',
        'biometric_method': 'fingerprint',
        'verification_type': 'check-in',
        'csrf_token': csrf_token
    }
    
    response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✅ Check-in successful - Time: {result.get('time_recorded')}")
        else:
            print(f"❌ Check-in failed: {result.get('error')}")
            return False
    else:
        print("❌ Check-in request failed")
        return False
    
    # Test 2: Check-out (should work after check-in)
    print("\n2️⃣ Testing Check-out (should work after check-in)...")
    csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
    verification_data['verification_type'] = 'check-out'
    verification_data['csrf_token'] = csrf_token
    
    response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✅ Check-out successful - Time: {result.get('time_recorded')}")
        else:
            print(f"❌ Check-out failed: {result.get('error')}")
            return False
    else:
        print("❌ Check-out request failed")
        return False
    
    # Test 3: Overtime-in (should fail before 17:00)
    print("\n3️⃣ Testing Overtime-in (should fail before 17:00)...")
    current_hour = datetime.now().hour
    
    csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
    verification_data['verification_type'] = 'overtime-in'
    verification_data['csrf_token'] = csrf_token
    
    response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
    if response.status_code == 200:
        result = response.json()
        if current_hour < 17:  # Before 5 PM
            if not result.get('success') and 'Overtime can only start after 17:00' in result.get('error', ''):
                print("✅ Overtime-in correctly rejected before 17:00")
                print(f"   Current time: {datetime.now().strftime('%H:%M')}")
                print(f"   Error message: {result.get('error')}")
            else:
                print(f"❌ Overtime-in should be rejected before 17:00, but got: {result}")
                return False
        else:  # After 5 PM
            if result.get('success'):
                print(f"✅ Overtime-in successful after 17:00 - Time: {result.get('time_recorded')}")
            else:
                print(f"❌ Overtime-in failed after 17:00: {result.get('error')}")
                return False
    else:
        print("❌ Overtime-in request failed")
        return False
    
    # Test 4: Test duplicate check-in (should fail)
    print("\n4️⃣ Testing Duplicate Check-in (should fail)...")
    csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
    verification_data['verification_type'] = 'check-in'
    verification_data['csrf_token'] = csrf_token
    
    response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
    if response.status_code == 200:
        result = response.json()
        if not result.get('success') and 'Already checked in' in result.get('error', ''):
            print("✅ Duplicate check-in correctly rejected")
            print(f"   Error message: {result.get('error')}")
        else:
            print(f"❌ Duplicate check-in should be rejected, but got: {result}")
            return False
    else:
        print("❌ Duplicate check-in request failed")
        return False
    
    # Test 5: Test duplicate check-out (should fail)
    print("\n5️⃣ Testing Duplicate Check-out (should fail)...")
    csrf_token = get_csrf_token(staff_session, f'{base_url}/staff/dashboard')
    verification_data['verification_type'] = 'check-out'
    verification_data['csrf_token'] = csrf_token
    
    response = staff_session.post(f'{base_url}/biometric_attendance', data=verification_data)
    if response.status_code == 200:
        result = response.json()
        if not result.get('success') and 'Already checked out' in result.get('error', ''):
            print("✅ Duplicate check-out correctly rejected")
            print(f"   Error message: {result.get('error')}")
        else:
            print(f"❌ Duplicate check-out should be rejected, but got: {result}")
            return False
    else:
        print("❌ Duplicate check-out request failed")
        return False
    
    # Test 6: Verify admin dashboard shows correct data
    print("\n6️⃣ Testing Admin Dashboard Shows Correct Data...")
    
    # Login as admin
    admin_session = requests.Session()
    csrf_token = get_csrf_token(admin_session, f'{base_url}/')
    
    login_data = {
        'school_id': '4',
        'username': 'admin',
        'password': 'admin',
        'csrf_token': csrf_token
    }
    
    response = admin_session.post(f'{base_url}/login', data=login_data)
    if response.status_code == 200 and response.json().get('redirect'):
        # Get real-time attendance data
        response = admin_session.get(f'{base_url}/get_realtime_attendance')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                # Find Mohan Raj's data
                mohan_raj_data = None
                for record in data.get('attendance_data', []):
                    if record.get('staff_number') == '888':
                        mohan_raj_data = record
                        break
                
                if mohan_raj_data:
                    print("✅ Admin dashboard shows correct attendance data:")
                    print(f"   Check-in: {mohan_raj_data.get('time_in')}")
                    print(f"   Check-out: {mohan_raj_data.get('time_out')}")
                    print(f"   Overtime-in: {mohan_raj_data.get('overtime_in') or 'Not recorded (correct - before 17:00)'}")
                    print(f"   Overtime-out: {mohan_raj_data.get('overtime_out') or 'Not recorded (correct - no overtime-in)'}")
                    print(f"   Status: {mohan_raj_data.get('status')}")
                    
                    # Verify check-in and check-out are recorded
                    if mohan_raj_data.get('time_in') and mohan_raj_data.get('time_out'):
                        print("✅ Both check-in and check-out are properly recorded")
                    else:
                        print("❌ Check-in or check-out missing")
                        return False
                else:
                    print("❌ Mohan Raj not found in admin dashboard")
                    return False
            else:
                print("❌ Failed to get real-time attendance data")
                return False
        else:
            print("❌ Failed to access real-time attendance endpoint")
            return False
    else:
        print("❌ Admin login failed")
        return False
    
    print("\n🎉 ALL BUSINESS LOGIC VALIDATION TESTS PASSED!")
    print("=" * 60)
    print("✅ Check-in works correctly (anytime)")
    print("✅ Check-out works correctly (after check-in)")
    print("✅ Overtime-in correctly rejected before 17:00")
    print("✅ Duplicate check-in correctly rejected")
    print("✅ Duplicate check-out correctly rejected")
    print("✅ Admin dashboard shows correct real-time data")
    print("\n💡 BUSINESS LOGIC IS WORKING PERFECTLY!")
    print("📋 Summary of Business Rules:")
    print("   • Check-in: Can be done anytime (marked late if after 9:00 AM)")
    print("   • Check-out: Requires prior check-in")
    print("   • Overtime-in: Requires prior check-out AND must be after 17:00")
    print("   • Overtime-out: Requires prior overtime-in")
    print("   • No duplicates allowed for any verification type")
    print("   • All verifications update admin dashboard in real-time")
    
    return True

if __name__ == "__main__":
    test_business_logic_validation()
